import { defineConfig } from 'vite'
import { resolve } from 'path'

export default defineConfig({
  build: {
    rollupOptions: {
      input: resolve(__dirname, '../packages/theme/index.scss'),
      output: {
        assetFileNames: 'index.css',
      },
    },
    outDir: resolve(__dirname, '../theme'),
    emptyOutDir: true,
  },
  css: {
    preprocessorOptions: {
      scss: {
        charset: false,
      },
    },
  },
})
