// Button 组件样式
// 基于 component-library.html 中的按钮样式

.lz-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--lz-space-2);
  border: none;
  border-radius: var(--lz-radius-lg);
  font-size: var(--lz-text-sm);
  font-weight: var(--lz-font-semibold);
  line-height: var(--lz-leading-none);
  padding: var(--lz-space-2-5) var(--lz-space-4);
  transition: all var(--lz-transition-base);
  cursor: pointer;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  outline: none;
  box-shadow: var(--lz-shadow-sm);
  white-space: nowrap;
  user-select: none;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left var(--lz-transition-slow);
  }

  &:hover::before {
    left: 100%;
  }

  &:disabled,
  &.lz-button--disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;

    &::before {
      display: none;
    }
  }

  &.lz-button--loading {
    cursor: not-allowed;
  }

  // 按钮尺寸
  &.lz-button--small {
    padding: var(--lz-space-1-5) var(--lz-space-3);
    font-size: var(--lz-text-xs);
    border-radius: var(--lz-radius);
  }

  &.lz-button--large {
    padding: var(--lz-space-3-5) var(--lz-space-6);
    font-size: var(--lz-text-base);
    border-radius: var(--lz-radius-xl);
  }

  // 按钮类型
  &.lz-button--primary {
    background: var(--lz-gradient-primary);
    color: white;

    &:hover:not(:disabled):not(.lz-button--disabled):not(.lz-button--loading) {
      transform: translateY(-2px);
      box-shadow: var(--lz-shadow-lg);
    }

    &:active {
      transform: translateY(0);
    }
  }

  &.lz-button--secondary {
    background: var(--lz-gradient-secondary);
    color: white;

    &:hover:not(:disabled):not(.lz-button--disabled):not(.lz-button--loading) {
      transform: translateY(-2px);
      box-shadow: var(--lz-shadow-lg);
    }
  }

  &.lz-button--outline {
    background: transparent;
    border: 2px solid var(--lz-primary-500);
    color: var(--lz-primary-600);
    box-shadow: none;

    &:hover:not(:disabled):not(.lz-button--disabled):not(.lz-button--loading) {
      background: var(--lz-primary-50);
      border-color: var(--lz-primary-600);
      transform: translateY(-1px);
      box-shadow: var(--lz-shadow);
    }
  }

  &.lz-button--ghost {
    background: transparent;
    color: var(--lz-gray-600);
    box-shadow: none;

    &:hover:not(:disabled):not(.lz-button--disabled):not(.lz-button--loading) {
      background: var(--lz-gray-100);
      color: var(--lz-gray-800);
    }
  }

  &.lz-button--danger {
    background: linear-gradient(135deg, var(--lz-error) 0%, #DC2626 100%);
    color: white;

    &:hover:not(:disabled):not(.lz-button--disabled):not(.lz-button--loading) {
      transform: translateY(-2px);
      box-shadow: 0 10px 15px -3px rgba(239, 68, 68, 0.3), 0 4px 6px -2px rgba(239, 68, 68, 0.1);
    }
  }

  &.lz-button--text {
    background: transparent;
    color: var(--lz-primary-600);
    box-shadow: none;
    padding: var(--lz-space-1) var(--lz-space-2);

    &:hover:not(:disabled):not(.lz-button--disabled):not(.lz-button--loading) {
      background: var(--lz-primary-50);
    }
  }

  &.lz-button--link {
    background: transparent;
    color: var(--lz-primary-600);
    box-shadow: none;
    padding: 0;
    text-decoration: underline;

    &:hover:not(:disabled):not(.lz-button--disabled):not(.lz-button--loading) {
      color: var(--lz-primary-700);
    }
  }

  // 圆角和圆形
  &.lz-button--round {
    border-radius: var(--lz-radius-full);
  }

  &.lz-button--circle {
    border-radius: var(--lz-radius-full);
    padding: var(--lz-space-2-5);
    width: 40px;
    height: 40px;

    &.lz-button--small {
      width: 32px;
      height: 32px;
      padding: var(--lz-space-1-5);
    }

    &.lz-button--large {
      width: 48px;
      height: 48px;
      padding: var(--lz-space-3-5);
    }
  }
}

// 按钮文本
.lz-button__text {
  display: inline-block;
}

// 加载图标
.lz-button__loading-icon {
  animation: lz-button-loading-spin 1s linear infinite;
}

@keyframes lz-button-loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
