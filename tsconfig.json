{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "allowJs": false,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    /* Vue */
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,

    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["packages/*"],
      "@lanzt-ui/*": ["packages/*"]
    },

    /* Declaration */
    "declaration": true,
    "declarationMap": true,
    "emitDeclarationOnly": false
  },
  "include": [
    "packages/**/*.ts",
    "packages/**/*.tsx",
    "packages/**/*.vue",
    "build/**/*.ts",
    "playground/**/*.ts",
    "playground/**/*.vue"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "lib",
    "es"
  ],
  "references": [
    { "path": "./tsconfig.node.json" }
  ]
}
