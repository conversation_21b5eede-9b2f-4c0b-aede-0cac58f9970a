<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue3组件库设计系统 - 完整版</title>
    <style>
        /* CSS变量定义 - 扩展版设计标记系统 */
        :root {
            /* 主色系 */
            --primary: #0FD59D;
            --primary-50: #ECFDF5;
            --primary-100: #D1FAE5;
            --primary-200: #A7F3D0;
            --primary-300: #6EE7B7;
            --primary-400: #34D399;
            --primary-500: #0FD59D;
            --primary-600: #059669;
            --primary-700: #047857;
            --primary-800: #065F46;
            --primary-900: #064E3B;
            
            /* 辅助色系 */
            --secondary: #4F46E5;
            --secondary-50: #EEF2FF;
            --secondary-100: #E0E7FF;
            --secondary-200: #C7D2FE;
            --secondary-300: #A5B4FC;
            --secondary-400: #818CF8;
            --secondary-500: #6366F1;
            --secondary-600: #4F46E5;
            --secondary-700: #4338CA;
            --secondary-800: #3730A3;
            --secondary-900: #312E81;
            
            /* 强调色系 */
            --accent: #F59E0B;
            --accent-50: #FFFBEB;
            --accent-100: #FEF3C7;
            --accent-200: #FDE68A;
            --accent-300: #FCD34D;
            --accent-400: #FBBF24;
            --accent-500: #F59E0B;
            --accent-600: #D97706;
            --accent-700: #B45309;
            --accent-800: #92400E;
            --accent-900: #78350F;
            
            /* 状态色系 */
            --success: #10B981;
            --success-light: #D1FAE5;
            --warning: #F59E0B;
            --warning-light: #FEF3C7;
            --error: #EF4444;
            --error-light: #FEE2E2;
            --info: #3B82F6;
            --info-light: #DBEAFE;
            
            /* 中性色系 */
            --gray-50: #F9FAFB;
            --gray-100: #F3F4F6;
            --gray-200: #E5E7EB;
            --gray-300: #D1D5DB;
            --gray-400: #9CA3AF;
            --gray-500: #6B7280;
            --gray-600: #4B5563;
            --gray-700: #374151;
            --gray-800: #1F2937;
            --gray-900: #111827;
            
            /* 暗色主题变量 */
            --dark-bg: #0F172A;
            --dark-surface: #1E293B;
            --dark-border: #334155;
            --dark-text: #F1F5F9;
            --dark-text-secondary: #94A3B8;
            
            /* 字体大小 */
            --text-xs: 0.75rem;
            --text-sm: 0.875rem;
            --text-base: 1rem;
            --text-lg: 1.125rem;
            --text-xl: 1.25rem;
            --text-2xl: 1.5rem;
            --text-3xl: 1.875rem;
            --text-4xl: 2.25rem;
            --text-5xl: 3rem;
            
            /* 字体权重 */
            --font-light: 300;
            --font-normal: 400;
            --font-medium: 500;
            --font-semibold: 600;
            --font-bold: 700;
            --font-extrabold: 800;
            
            /* 行高 */
            --leading-none: 1;
            --leading-tight: 1.25;
            --leading-snug: 1.375;
            --leading-normal: 1.5;
            --leading-relaxed: 1.625;
            --leading-loose: 2;
            
            /* 间距 */
            --space-0: 0;
            --space-px: 1px;
            --space-0-5: 0.125rem;
            --space-1: 0.25rem;
            --space-1-5: 0.375rem;
            --space-2: 0.5rem;
            --space-2-5: 0.625rem;
            --space-3: 0.75rem;
            --space-3-5: 0.875rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-7: 1.75rem;
            --space-8: 2rem;
            --space-9: 2.25rem;
            --space-10: 2.5rem;
            --space-11: 2.75rem;
            --space-12: 3rem;
            --space-14: 3.5rem;
            --space-16: 4rem;
            --space-20: 5rem;
            --space-24: 6rem;
            --space-28: 7rem;
            --space-32: 8rem;
            
            /* 圆角 */
            --radius-none: 0;
            --radius-sm: 0.125rem;
            --radius: 0.25rem;
            --radius-md: 0.375rem;
            --radius-lg: 0.5rem;
            --radius-xl: 0.75rem;
            --radius-2xl: 1rem;
            --radius-3xl: 1.5rem;
            --radius-full: 9999px;
            
            /* 阴影 */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
            
            /* 渐变 */
            --gradient-primary: linear-gradient(135deg, var(--primary-400) 0%, var(--primary-600) 100%);
            --gradient-secondary: linear-gradient(135deg, var(--secondary-400) 0%, var(--secondary-600) 100%);
            --gradient-warm: linear-gradient(135deg, var(--accent-400) 0%, var(--error) 100%);
            --gradient-cool: linear-gradient(135deg, var(--primary-400) 0%, var(--secondary-500) 100%);
            
            /* 过渡动画 */
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slower: 500ms cubic-bezier(0.4, 0, 0.2, 1);
            
            /* Z-index层级 */
            --z-dropdown: 1000;
            --z-sticky: 1020;
            --z-fixed: 1030;
            --z-modal-backdrop: 1040;
            --z-modal: 1050;
            --z-popover: 1060;
            --z-tooltip: 1070;
            --z-toast: 1080;
        }
        
        /* 暗色主题 */
        [data-theme="dark"] {
            --gray-50: var(--dark-text);
            --gray-100: #E2E8F0;
            --gray-200: #CBD5E1;
            --gray-300: #94A3B8;
            --gray-400: #64748B;
            --gray-500: #475569;
            --gray-600: #334155;
            --gray-700: #1E293B;
            --gray-800: #0F172A;
            --gray-900: var(--dark-bg);
        }
        
        /* 基础样式重置 */
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: var(--leading-normal);
            color: var(--gray-800);
            background-color: var(--gray-50);
            margin: 0;
            padding: var(--space-6);
            font-size: var(--text-base);
        }
        
        /* 容器和布局 */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-4);
        }
        
        .section {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--space-8);
            margin: var(--space-8) 0;
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
        }
        
        /* 标题样式 */
        h1, h2, h3, h4, h5, h6 {
            color: var(--gray-900);
            font-weight: var(--font-bold);
            line-height: var(--leading-tight);
            margin: 0;
        }
        
        h1 { font-size: var(--text-4xl); margin-bottom: var(--space-6); }
        h2 { font-size: var(--text-3xl); margin-bottom: var(--space-5); }
        h3 { font-size: var(--text-2xl); margin-bottom: var(--space-4); }
        h4 { font-size: var(--text-xl); margin-bottom: var(--space-3); }
        
        /* 工具类 */
        .flex { display: flex; }
        .flex-col { flex-direction: column; }
        .flex-wrap { flex-wrap: wrap; }
        .items-center { align-items: center; }
        .items-start { align-items: flex-start; }
        .justify-center { justify-content: center; }
        .justify-between { justify-content: space-between; }
        .justify-start { justify-content: flex-start; }
        .gap-2 { gap: var(--space-2); }
        .gap-3 { gap: var(--space-3); }
        .gap-4 { gap: var(--space-4); }
        .gap-6 { gap: var(--space-6); }
        
        .grid { display: grid; }
        .grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
        .grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
        .grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
        .grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
        
        .text-center { text-align: center; }
        .text-left { text-align: left; }
        .text-right { text-align: right; }
        
        .mb-2 { margin-bottom: var(--space-2); }
        .mb-3 { margin-bottom: var(--space-3); }
        .mb-4 { margin-bottom: var(--space-4); }
        .mb-6 { margin-bottom: var(--space-6); }
        .mt-4 { margin-top: var(--space-4); }
        .ml-2 { margin-left: var(--space-2); }
        .mr-2 { margin-right: var(--space-2); }
        
        .p-4 { padding: var(--space-4); }
        .p-6 { padding: var(--space-6); }
        .px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }
        .py-2 { padding-top: var(--space-2); padding-bottom: var(--space-2); }
        .py-3 { padding-top: var(--space-3); padding-bottom: var(--space-3); }
        
        .w-full { width: 100%; }
        .h-full { height: 100%; }
        
        .relative { position: relative; }
        .absolute { position: absolute; }
        .fixed { position: fixed; }
        
        .hidden { display: none; }
        .block { display: block; }
        .inline-block { display: inline-block; }
        
        .opacity-50 { opacity: 0.5; }
        .opacity-75 { opacity: 0.75; }
        
        .cursor-pointer { cursor: pointer; }
        .cursor-not-allowed { cursor: not-allowed; }
        
        .select-none { user-select: none; }
        
        .overflow-hidden { overflow: hidden; }
        .overflow-auto { overflow: auto; }
        
        .whitespace-nowrap { white-space: nowrap; }
        .truncate {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* ===== 表单组件样式 ===== */

        /* 输入框基础样式 */
        .lz-input {
            width: 100%;
            border: 2px solid var(--gray-300);
            border-radius: var(--radius-lg);
            background-color: white;
            color: var(--gray-900);
            font-size: var(--text-sm);
            font-weight: var(--font-normal);
            line-height: var(--leading-normal);
            padding: var(--space-2-5) var(--space-3-5);
            transition: all var(--transition-base);
            outline: none;
            box-shadow: var(--shadow-sm);
        }

        .lz-input::placeholder {
            color: var(--gray-400);
        }

        .lz-input:hover:not(:disabled) {
            border-color: var(--gray-400);
            box-shadow: var(--shadow);
        }

        .lz-input:focus {
            border-color: var(--primary-500);
            box-shadow: 0 0 0 3px rgba(15, 213, 157, 0.1), var(--shadow);
            transform: translateY(-1px);
        }

        .lz-input:disabled {
            background-color: var(--gray-100);
            border-color: var(--gray-200);
            color: var(--gray-400);
            cursor: not-allowed;
            opacity: 0.6;
        }

        /* 输入框尺寸变体 */
        .lz-input--small {
            padding: var(--space-1-5) var(--space-2-5);
            font-size: var(--text-xs);
            border-radius: var(--radius);
        }

        .lz-input--large {
            padding: var(--space-3-5) var(--space-4);
            font-size: var(--text-base);
            border-radius: var(--radius-xl);
        }

        /* 输入框状态变体 */
        .lz-input--success {
            border-color: var(--success);
            background-color: var(--success-light);
        }

        .lz-input--success:focus {
            border-color: var(--success);
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1), var(--shadow);
        }

        .lz-input--warning {
            border-color: var(--warning);
            background-color: var(--warning-light);
        }

        .lz-input--warning:focus {
            border-color: var(--warning);
            box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1), var(--shadow);
        }

        .lz-input--error {
            border-color: var(--error);
            background-color: var(--error-light);
        }

        .lz-input--error:focus {
            border-color: var(--error);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1), var(--shadow);
        }

        .lz-input--loading {
            background-image: linear-gradient(90deg, transparent, rgba(15, 213, 157, 0.1), transparent);
            background-size: 200% 100%;
            animation: loading-shimmer 1.5s infinite;
        }

        @keyframes loading-shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* 自定义下拉选择器样式 */
        .lz-select {
            position: relative;
            width: 100%;
        }

        .lz-select__trigger {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            border: 2px solid var(--gray-300);
            border-radius: var(--radius-lg);
            background-color: white;
            color: var(--gray-900);
            font-size: var(--text-sm);
            font-weight: var(--font-normal);
            padding: var(--space-2-5) var(--space-3-5);
            transition: all var(--transition-base);
            outline: none;
            box-shadow: var(--shadow-sm);
            cursor: pointer;
            user-select: none;
            min-height: 40px;
        }

        .lz-select__trigger:hover:not(.lz-select__trigger--disabled) {
            border-color: var(--gray-400);
            box-shadow: var(--shadow);
        }

        .lz-select__trigger--focused {
            border-color: var(--primary-500);
            box-shadow: 0 0 0 3px rgba(15, 213, 157, 0.1), var(--shadow);
        }

        .lz-select__trigger--disabled {
            background-color: var(--gray-100);
            border-color: var(--gray-200);
            color: var(--gray-400);
            cursor: not-allowed;
            opacity: 0.6;
        }

        .lz-select__value {
            flex: 1;
            text-align: left;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .lz-select__placeholder {
            color: var(--gray-400);
        }

        .lz-select__arrow {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            color: var(--gray-500);
            transition: all var(--transition-base);
            font-size: var(--text-sm);
        }

        .lz-select__arrow--open {
            transform: rotate(180deg);
            color: var(--primary-500);
        }

        .lz-select__dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: var(--z-dropdown);
            background: white;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            margin-top: var(--space-1);
            max-height: 200px;
            overflow-y: auto;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all var(--transition-base);
        }

        .lz-select__dropdown--open {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .lz-select__search {
            position: sticky;
            top: 0;
            background: white;
            border-bottom: 1px solid var(--gray-200);
            padding: var(--space-2);
        }

        .lz-select__search-input {
            width: 100%;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius);
            padding: var(--space-2) var(--space-3);
            font-size: var(--text-sm);
            outline: none;
            transition: border-color var(--transition-base);
        }

        .lz-select__search-input:focus {
            border-color: var(--primary-500);
        }

        .lz-select__options {
            list-style: none;
            margin: 0;
            padding: var(--space-1) 0;
        }

        .lz-select__option {
            display: flex;
            align-items: center;
            padding: var(--space-2-5) var(--space-3-5);
            font-size: var(--text-sm);
            color: var(--gray-700);
            cursor: pointer;
            transition: all var(--transition-base);
            user-select: none;
        }

        .lz-select__option:hover {
            background-color: var(--primary-50);
            color: var(--primary-700);
        }

        .lz-select__option--selected {
            background-color: var(--primary-100);
            color: var(--primary-800);
            font-weight: var(--font-medium);
        }

        .lz-select__option--focused {
            background-color: var(--primary-50);
            color: var(--primary-700);
        }

        .lz-select__option--disabled {
            color: var(--gray-400);
            cursor: not-allowed;
            opacity: 0.6;
        }

        .lz-select__option--disabled:hover {
            background-color: transparent;
            color: var(--gray-400);
        }

        .lz-select__option-icon {
            margin-right: var(--space-2);
            font-size: var(--text-base);
        }

        .lz-select__option-text {
            flex: 1;
        }

        .lz-select__option-description {
            font-size: var(--text-xs);
            color: var(--gray-500);
            margin-top: var(--space-0-5);
        }

        .lz-select__option-check {
            margin-left: var(--space-2);
            color: var(--primary-500);
            font-size: var(--text-sm);
        }

        .lz-select__group {
            padding: var(--space-2) var(--space-3-5) var(--space-1);
            font-size: var(--text-xs);
            font-weight: var(--font-semibold);
            color: var(--gray-500);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            background-color: var(--gray-50);
            border-bottom: 1px solid var(--gray-200);
        }

        .lz-select__empty {
            padding: var(--space-4);
            text-align: center;
            color: var(--gray-500);
            font-size: var(--text-sm);
        }

        /* 多选模式样式 */
        .lz-select--multiple .lz-select__trigger {
            min-height: 40px;
            height: auto;
            flex-wrap: wrap;
            gap: var(--space-1);
            padding: var(--space-1-5) var(--space-3-5);
        }

        .lz-select__tag {
            display: inline-flex;
            align-items: center;
            gap: var(--space-1);
            background-color: var(--primary-100);
            color: var(--primary-800);
            padding: var(--space-0-5) var(--space-2);
            border-radius: var(--radius);
            font-size: var(--text-xs);
            font-weight: var(--font-medium);
        }

        .lz-select__tag-close {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 16px;
            height: 16px;
            border-radius: var(--radius-full);
            background-color: var(--primary-200);
            color: var(--primary-700);
            cursor: pointer;
            transition: all var(--transition-base);
            font-size: 10px;
        }

        .lz-select__tag-close:hover {
            background-color: var(--primary-300);
        }

        /* 尺寸变体 */
        .lz-select--small .lz-select__trigger {
            padding: var(--space-1-5) var(--space-2-5);
            font-size: var(--text-xs);
            border-radius: var(--radius);
            min-height: 32px;
        }

        .lz-select--small .lz-select__option {
            padding: var(--space-2) var(--space-2-5);
            font-size: var(--text-xs);
        }

        .lz-select--large .lz-select__trigger {
            padding: var(--space-3-5) var(--space-4);
            font-size: var(--text-base);
            border-radius: var(--radius-xl);
            min-height: 48px;
        }

        .lz-select--large .lz-select__option {
            padding: var(--space-3) var(--space-4);
            font-size: var(--text-base);
        }

        /* 状态变体 */
        .lz-select--error .lz-select__trigger {
            border-color: var(--error);
            background-color: var(--error-light);
        }

        .lz-select--error .lz-select__trigger:focus {
            border-color: var(--error);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1), var(--shadow);
        }

        .lz-select--success .lz-select__trigger {
            border-color: var(--success);
            background-color: var(--success-light);
        }

        .lz-select--warning .lz-select__trigger {
            border-color: var(--warning);
            background-color: var(--warning-light);
        }

        /* 文本域样式 */
        .lz-textarea {
            width: 100%;
            border: 2px solid var(--gray-300);
            border-radius: var(--radius-lg);
            background-color: white;
            color: var(--gray-900);
            font-size: var(--text-sm);
            font-weight: var(--font-normal);
            line-height: var(--leading-relaxed);
            padding: var(--space-3) var(--space-3-5);
            transition: all var(--transition-base);
            outline: none;
            box-shadow: var(--shadow-sm);
            resize: vertical;
            min-height: 80px;
            font-family: inherit;
        }

        .lz-textarea::placeholder {
            color: var(--gray-400);
        }

        .lz-textarea:hover:not(:disabled) {
            border-color: var(--gray-400);
            box-shadow: var(--shadow);
        }

        .lz-textarea:focus {
            border-color: var(--primary-500);
            box-shadow: 0 0 0 3px rgba(15, 213, 157, 0.1), var(--shadow);
            transform: translateY(-1px);
        }

        .lz-textarea:disabled {
            background-color: var(--gray-100);
            border-color: var(--gray-200);
            color: var(--gray-400);
            cursor: not-allowed;
            opacity: 0.6;
            resize: none;
        }

        .lz-textarea--error {
            border-color: var(--error);
            background-color: var(--error-light);
        }

        .lz-textarea--error:focus {
            border-color: var(--error);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1), var(--shadow);
        }

        /* ===== 按钮组件样式 ===== */

        /* 按钮基础样式 */
        .lz-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
            border: none;
            border-radius: var(--radius-lg);
            font-size: var(--text-sm);
            font-weight: var(--font-semibold);
            line-height: var(--leading-none);
            padding: var(--space-2-5) var(--space-4);
            transition: all var(--transition-base);
            cursor: pointer;
            text-decoration: none;
            position: relative;
            overflow: hidden;
            outline: none;
            box-shadow: var(--shadow-sm);
            white-space: nowrap;
        }

        .lz-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left var(--transition-slow);
        }

        .lz-button:hover::before {
            left: 100%;
        }

        .lz-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .lz-button:disabled::before {
            display: none;
        }

        /* 按钮尺寸变体 */
        .lz-button--small {
            padding: var(--space-1-5) var(--space-3);
            font-size: var(--text-xs);
            border-radius: var(--radius);
        }

        .lz-button--large {
            padding: var(--space-3-5) var(--space-6);
            font-size: var(--text-base);
            border-radius: var(--radius-xl);
        }

        /* 按钮类型变体 */
        .lz-button--primary {
            background: var(--gradient-primary);
            color: white;
        }

        .lz-button--primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .lz-button--primary:active {
            transform: translateY(0);
        }

        .lz-button--secondary {
            background: var(--gradient-secondary);
            color: white;
        }

        .lz-button--secondary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .lz-button--outline {
            background: transparent;
            border: 2px solid var(--primary-500);
            color: var(--primary-600);
            box-shadow: none;
        }

        .lz-button--outline:hover:not(:disabled) {
            background: var(--primary-50);
            border-color: var(--primary-600);
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .lz-button--ghost {
            background: transparent;
            color: var(--gray-600);
            box-shadow: none;
        }

        .lz-button--ghost:hover:not(:disabled) {
            background: var(--gray-100);
            color: var(--gray-800);
        }

        .lz-button--danger {
            background: linear-gradient(135deg, var(--error) 0%, #DC2626 100%);
            color: white;
        }

        .lz-button--danger:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(239, 68, 68, 0.3), 0 4px 6px -2px rgba(239, 68, 68, 0.1);
        }

        /* 加载动画 */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* ===== 导航组件样式 ===== */

        /* 面包屑导航样式 */
        .lz-breadcrumb {
            font-size: var(--text-sm);
        }

        .lz-breadcrumb__list {
            display: flex;
            align-items: center;
            list-style: none;
            margin: 0;
            padding: 0;
            gap: var(--space-1);
        }

        .lz-breadcrumb__item {
            display: flex;
            align-items: center;
        }

        .lz-breadcrumb__item:not(:last-child)::after {
            content: '/';
            margin: 0 var(--space-2);
            color: var(--gray-400);
            font-weight: var(--font-normal);
        }

        .lz-breadcrumb__link {
            color: var(--gray-600);
            text-decoration: none;
            transition: color var(--transition-base);
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius);
        }

        .lz-breadcrumb__link:hover {
            color: var(--primary-600);
            background-color: var(--primary-50);
        }

        .lz-breadcrumb__item--current .lz-breadcrumb__text {
            color: var(--gray-900);
            font-weight: var(--font-medium);
            padding: var(--space-1) var(--space-2);
        }

        /* 分页器样式 */
        .lz-pagination {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: var(--text-sm);
        }

        .lz-pagination__button {
            display: flex;
            align-items: center;
            gap: var(--space-1);
            padding: var(--space-2) var(--space-3);
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-lg);
            background: white;
            color: var(--gray-700);
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
            cursor: pointer;
            transition: all var(--transition-base);
            text-decoration: none;
        }

        .lz-pagination__button:hover:not(:disabled) {
            border-color: var(--primary-500);
            color: var(--primary-600);
            background-color: var(--primary-50);
        }

        .lz-pagination__button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background-color: var(--gray-100);
        }

        .lz-pagination__pages {
            display: flex;
            align-items: center;
            gap: var(--space-1);
        }

        .lz-pagination__page {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-lg);
            background: white;
            color: var(--gray-700);
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
            cursor: pointer;
            transition: all var(--transition-base);
            text-decoration: none;
        }

        .lz-pagination__page:hover {
            border-color: var(--primary-500);
            color: var(--primary-600);
            background-color: var(--primary-50);
        }

        .lz-pagination__page--current {
            border-color: var(--primary-500);
            background: var(--gradient-primary);
            color: white;
        }

        .lz-pagination__page--current:hover {
            background: var(--gradient-primary);
            color: white;
        }

        .lz-pagination__ellipsis {
            padding: var(--space-2);
            color: var(--gray-400);
        }

        /* 步骤条样式 */
        .lz-steps {
            display: flex;
            align-items: flex-start;
            gap: var(--space-8);
        }

        .lz-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            flex: 1;
        }

        .lz-step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 20px;
            left: calc(50% + 20px);
            right: calc(-50% + 20px);
            height: 2px;
            background-color: var(--gray-300);
            z-index: 1;
        }

        .lz-step--completed:not(:last-child)::after {
            background-color: var(--primary-500);
        }

        .lz-step__icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: var(--radius-full);
            background-color: var(--gray-300);
            color: white;
            font-size: var(--text-sm);
            font-weight: var(--font-semibold);
            margin-bottom: var(--space-3);
            position: relative;
            z-index: 2;
            transition: all var(--transition-base);
        }

        .lz-step--completed .lz-step__icon {
            background: var(--gradient-primary);
        }

        .lz-step--current .lz-step__icon {
            background: var(--gradient-primary);
            box-shadow: 0 0 0 4px rgba(15, 213, 157, 0.2);
        }

        .lz-step__content {
            text-align: center;
        }

        .lz-step__title {
            font-size: var(--text-sm);
            font-weight: var(--font-semibold);
            color: var(--gray-900);
            margin-bottom: var(--space-1);
        }

        .lz-step__description {
            font-size: var(--text-xs);
            color: var(--gray-600);
            line-height: var(--leading-relaxed);
        }

        .lz-step--current .lz-step__title {
            color: var(--primary-600);
        }

        /* ===== 数据展示组件样式 ===== */

        /* 徽章组件样式 */
        .lz-badge {
            display: inline-flex;
            align-items: center;
            padding: var(--space-1) var(--space-2-5);
            border-radius: var(--radius-full);
            font-size: var(--text-xs);
            font-weight: var(--font-semibold);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            white-space: nowrap;
        }

        .lz-badge--primary {
            background-color: var(--primary-100);
            color: var(--primary-800);
        }

        .lz-badge--secondary {
            background-color: var(--secondary-100);
            color: var(--secondary-800);
        }

        .lz-badge--success {
            background-color: var(--success-light);
            color: var(--primary-800);
        }

        .lz-badge--warning {
            background-color: var(--warning-light);
            color: var(--accent-800);
        }

        .lz-badge--error {
            background-color: var(--error-light);
            color: var(--error);
        }

        .lz-badge--info {
            background-color: var(--info-light);
            color: var(--info);
        }

        /* 表格组件样式 */
        .lz-table-container {
            overflow-x: auto;
            border-radius: var(--radius-xl);
            border: 1px solid var(--gray-200);
            box-shadow: var(--shadow-sm);
        }

        .lz-table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
        }

        .lz-table__header {
            background-color: var(--gray-50);
        }

        .lz-table__cell {
            padding: var(--space-3) var(--space-4);
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
            font-size: var(--text-sm);
            line-height: var(--leading-relaxed);
        }

        .lz-table__cell--header {
            font-weight: var(--font-semibold);
            color: var(--gray-900);
            background-color: var(--gray-50);
            border-bottom: 2px solid var(--gray-300);
        }

        .lz-table__row {
            transition: background-color var(--transition-base);
        }

        .lz-table__row:hover {
            background-color: var(--primary-50);
        }

        .lz-table__row:last-child .lz-table__cell {
            border-bottom: none;
        }

        /* 标签页组件样式 */
        .lz-tabs {
            background: white;
            border-radius: var(--radius-xl);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
        }

        .lz-tabs__nav {
            display: flex;
            background-color: var(--gray-50);
            border-bottom: 1px solid var(--gray-200);
        }

        .lz-tabs__tab {
            flex: 1;
            padding: var(--space-3) var(--space-4);
            border: none;
            background: transparent;
            color: var(--gray-600);
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
            cursor: pointer;
            transition: all var(--transition-base);
            position: relative;
        }

        .lz-tabs__tab:hover {
            color: var(--primary-600);
            background-color: var(--primary-50);
        }

        .lz-tabs__tab--active {
            color: var(--primary-600);
            background-color: white;
        }

        .lz-tabs__tab--active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--gradient-primary);
        }

        .lz-tabs__content {
            padding: var(--space-6);
        }

        .lz-tabs__panel {
            display: none;
        }

        .lz-tabs__panel--active {
            display: block;
        }

        /* 折叠面板样式 */
        .lz-collapse {
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-xl);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .lz-collapse__item {
            border-bottom: 1px solid var(--gray-200);
        }

        .lz-collapse__item:last-child {
            border-bottom: none;
        }

        .lz-collapse__header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--space-4) var(--space-6);
            background-color: var(--gray-50);
            cursor: pointer;
            transition: all var(--transition-base);
        }

        .lz-collapse__header:hover {
            background-color: var(--primary-50);
        }

        .lz-collapse__title {
            margin: 0;
            font-size: var(--text-base);
            font-weight: var(--font-semibold);
            color: var(--gray-900);
        }

        .lz-collapse__icon {
            color: var(--gray-500);
            font-size: var(--text-sm);
            transition: transform var(--transition-base);
        }

        .lz-collapse__content {
            max-height: 0;
            overflow: hidden;
            transition: max-height var(--transition-slow);
            background-color: white;
        }

        .lz-collapse__content--expanded {
            max-height: 500px;
            padding: var(--space-4) var(--space-6);
        }

        .lz-collapse__content p,
        .lz-collapse__content ul {
            margin: 0;
            color: var(--gray-700);
            line-height: var(--leading-relaxed);
        }

        .lz-collapse__content ul {
            padding-left: var(--space-5);
        }

        .lz-collapse__content li {
            margin-bottom: var(--space-1);
        }

        /* ===== 反馈组件样式 ===== */

        /* 模态框样式 */
        .lz-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: var(--z-modal);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-4);
        }

        .lz-modal__backdrop {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
        }

        .lz-modal__container {
            position: relative;
            z-index: 1;
            max-width: 500px;
            width: 100%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .lz-modal__content {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-2xl);
            overflow: hidden;
        }

        .lz-modal__header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--space-6);
            border-bottom: 1px solid var(--gray-200);
        }

        .lz-modal__title {
            margin: 0;
            font-size: var(--text-xl);
            font-weight: var(--font-semibold);
            color: var(--gray-900);
        }

        .lz-modal__close {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border: none;
            border-radius: var(--radius-full);
            background: var(--gray-100);
            color: var(--gray-500);
            font-size: var(--text-xl);
            cursor: pointer;
            transition: all var(--transition-base);
        }

        .lz-modal__close:hover {
            background: var(--gray-200);
            color: var(--gray-700);
        }

        .lz-modal__body {
            padding: var(--space-6);
            color: var(--gray-700);
            line-height: var(--leading-relaxed);
        }

        .lz-modal__footer {
            display: flex;
            justify-content: flex-end;
            gap: var(--space-3);
            padding: var(--space-6);
            border-top: 1px solid var(--gray-200);
            background-color: var(--gray-50);
        }

        /* 通知容器样式 */
        .lz-notification-container {
            position: fixed;
            top: var(--space-6);
            right: var(--space-6);
            z-index: var(--z-toast);
            display: flex;
            flex-direction: column;
            gap: var(--space-3);
            max-width: 400px;
        }

        .lz-notification {
            display: flex;
            align-items: flex-start;
            gap: var(--space-3);
            padding: var(--space-4);
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            border-left: 4px solid;
            animation: slideInRight 0.3s ease-out;
        }

        .lz-notification--success {
            border-left-color: var(--success);
        }

        .lz-notification--info {
            border-left-color: var(--info);
        }

        .lz-notification--warning {
            border-left-color: var(--warning);
        }

        .lz-notification--error {
            border-left-color: var(--error);
        }

        .lz-notification__icon {
            font-size: var(--text-lg);
            margin-top: var(--space-0-5);
        }

        .lz-notification__content {
            flex: 1;
        }

        .lz-notification__title {
            font-size: var(--text-sm);
            font-weight: var(--font-semibold);
            color: var(--gray-900);
            margin-bottom: var(--space-1);
        }

        .lz-notification__text {
            font-size: var(--text-sm);
            color: var(--gray-600);
            line-height: var(--leading-relaxed);
        }

        .lz-notification__close {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border: none;
            border-radius: var(--radius-full);
            background: transparent;
            color: var(--gray-400);
            font-size: var(--text-lg);
            cursor: pointer;
            transition: all var(--transition-base);
        }

        .lz-notification__close:hover {
            background: var(--gray-100);
            color: var(--gray-600);
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* 消息提示样式 */
        .lz-message {
            display: flex;
            align-items: flex-start;
            gap: var(--space-3);
            padding: var(--space-4);
            margin-bottom: var(--space-4);
            border-radius: var(--radius-xl);
            border: 1px solid;
        }

        .lz-message--info {
            background-color: var(--info-light);
            border-color: var(--info);
            color: var(--info);
        }

        .lz-message--warning {
            background-color: var(--warning-light);
            border-color: var(--warning);
            color: var(--accent-800);
        }

        .lz-message--success {
            background-color: var(--success-light);
            border-color: var(--success);
            color: var(--primary-800);
        }

        .lz-message--error {
            background-color: var(--error-light);
            border-color: var(--error);
            color: var(--error);
        }

        .lz-message__icon {
            font-size: var(--text-lg);
            margin-top: var(--space-0-5);
        }

        .lz-message__content {
            flex: 1;
        }

        .lz-message__title {
            font-size: var(--text-sm);
            font-weight: var(--font-semibold);
            margin-bottom: var(--space-1);
        }

        .lz-message__text {
            font-size: var(--text-sm);
            line-height: var(--leading-relaxed);
            opacity: 0.9;
        }

        .lz-message__close {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border: none;
            border-radius: var(--radius-full);
            background: transparent;
            font-size: var(--text-lg);
            cursor: pointer;
            transition: all var(--transition-base);
            opacity: 0.6;
        }

        .lz-message__close:hover {
            opacity: 1;
            background: rgba(0, 0, 0, 0.1);
        }

        /* ===== 交互组件样式 ===== */

        /* 开关组件样式 */
        .lz-switch {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            cursor: pointer;
            user-select: none;
        }

        .lz-switch input {
            display: none;
        }

        .lz-switch__slider {
            position: relative;
            width: 48px;
            height: 24px;
            background-color: var(--gray-300);
            border-radius: var(--radius-full);
            transition: all var(--transition-base);
        }

        .lz-switch__slider::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background-color: white;
            border-radius: var(--radius-full);
            transition: all var(--transition-base);
            box-shadow: var(--shadow-sm);
        }

        .lz-switch input:checked + .lz-switch__slider {
            background: var(--gradient-primary);
        }

        .lz-switch input:checked + .lz-switch__slider::before {
            transform: translateX(24px);
        }

        .lz-switch input:disabled + .lz-switch__slider {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .lz-switch--small .lz-switch__slider {
            width: 36px;
            height: 20px;
        }

        .lz-switch--small .lz-switch__slider::before {
            width: 16px;
            height: 16px;
        }

        .lz-switch--small input:checked + .lz-switch__slider::before {
            transform: translateX(16px);
        }

        .lz-switch__label {
            font-size: var(--text-sm);
            color: var(--gray-700);
            font-weight: var(--font-medium);
        }

        /* 滑块组件样式 */
        .lz-slider {
            position: relative;
            width: 100%;
            height: 40px;
            display: flex;
            align-items: center;
        }

        .lz-slider__input {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
            z-index: 2;
        }

        .lz-slider__track {
            position: relative;
            width: 100%;
            height: 6px;
            background-color: var(--gray-300);
            border-radius: var(--radius-full);
            overflow: hidden;
        }

        .lz-slider__progress {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: var(--gradient-primary);
            border-radius: var(--radius-full);
            transition: width var(--transition-base);
        }

        .lz-slider__thumb {
            position: absolute;
            top: 50%;
            width: 20px;
            height: 20px;
            background: white;
            border: 3px solid var(--primary-500);
            border-radius: var(--radius-full);
            transform: translate(-50%, -50%);
            cursor: pointer;
            transition: all var(--transition-base);
            box-shadow: var(--shadow);
        }

        .lz-slider__thumb:hover {
            transform: translate(-50%, -50%) scale(1.1);
            box-shadow: var(--shadow-lg);
        }

        /* 评分组件样式 */
        .lz-rating {
            display: flex;
            align-items: center;
            gap: var(--space-1);
        }

        .lz-rating__star {
            font-size: var(--text-xl);
            color: var(--gray-300);
            cursor: pointer;
            transition: all var(--transition-base);
            user-select: none;
        }

        .lz-rating__star:hover {
            transform: scale(1.1);
        }

        .lz-rating__star--filled {
            color: var(--accent-500);
        }

        .lz-rating__star--half {
            background: linear-gradient(90deg, var(--accent-500) 50%, var(--gray-300) 50%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .lz-rating--small .lz-rating__star {
            font-size: var(--text-base);
        }

        .lz-rating__text {
            margin-left: var(--space-2);
            font-size: var(--text-sm);
            color: var(--gray-600);
            font-weight: var(--font-medium);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 var(--space-2);
            }

            .section {
                padding: var(--space-6);
                margin: var(--space-4) 0;
            }

            .grid-cols-2,
            .grid-cols-3,
            .grid-cols-4 {
                grid-template-columns: 1fr;
            }

            .lz-modal__container {
                margin: var(--space-4);
            }

            .lz-notification-container {
                top: var(--space-4);
                right: var(--space-4);
                left: var(--space-4);
                max-width: none;
            }

            .lz-steps {
                flex-direction: column;
                gap: var(--space-4);
            }

            .lz-step:not(:last-child)::after {
                display: none;
            }

            .lz-tabs__nav {
                flex-direction: column;
            }

            .lz-table-container {
                font-size: var(--text-xs);
            }

            .lz-table__cell {
                padding: var(--space-2) var(--space-3);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="text-center mb-6">
            <h1>Vue3组件库设计系统</h1>
            <p style="font-size: var(--text-lg); color: var(--gray-600); margin-top: var(--space-4);">
                基于 #0FD59D 主色调的完整组件库设计规范
            </p>
            <div class="flex justify-center gap-3 mt-4">
                <span style="background: var(--primary-100); color: var(--primary-800); padding: var(--space-1) var(--space-3); border-radius: var(--radius-full); font-size: var(--text-xs); font-weight: var(--font-semibold);">v2.0</span>
                <span style="background: var(--success-light); color: var(--primary-800); padding: var(--space-1) var(--space-3); border-radius: var(--radius-full); font-size: var(--text-xs); font-weight: var(--font-semibold);">完整版</span>
                <span style="background: var(--secondary-100); color: var(--secondary-800); padding: var(--space-1) var(--space-3); border-radius: var(--radius-full); font-size: var(--text-xs); font-weight: var(--font-semibold);">Vue3 Ready</span>
            </div>
        </header>

        <!-- 组件状态和尺寸规范 -->
        <div class="section">
            <h2>🎛️ 组件状态与尺寸规范</h2>
            <div class="grid grid-cols-2 gap-6">
                <div>
                    <h3>组件状态 (Component States)</h3>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin: var(--space-2) 0; padding: var(--space-2); background: var(--gray-100); border-radius: var(--radius);">
                            <strong>默认 (Default):</strong> 组件的初始状态
                        </li>
                        <li style="margin: var(--space-2) 0; padding: var(--space-2); background: var(--primary-50); border-radius: var(--radius);">
                            <strong>悬停 (Hover):</strong> 鼠标悬停时的状态
                        </li>
                        <li style="margin: var(--space-2) 0; padding: var(--space-2); background: var(--primary-100); border-radius: var(--radius);">
                            <strong>激活 (Active):</strong> 点击或选中时的状态
                        </li>
                        <li style="margin: var(--space-2) 0; padding: var(--space-2); background: var(--gray-200); border-radius: var(--radius);">
                            <strong>禁用 (Disabled):</strong> 不可交互的状态
                        </li>
                        <li style="margin: var(--space-2) 0; padding: var(--space-2); background: var(--secondary-50); border-radius: var(--radius);">
                            <strong>加载 (Loading):</strong> 处理中的状态
                        </li>
                        <li style="margin: var(--space-2) 0; padding: var(--space-2); background: var(--error-light); border-radius: var(--radius);">
                            <strong>错误 (Error):</strong> 验证失败的状态
                        </li>
                    </ul>
                </div>
                <div>
                    <h3>组件尺寸 (Component Sizes)</h3>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin: var(--space-2) 0; padding: var(--space-1-5); background: var(--gray-100); border-radius: var(--radius-sm); font-size: var(--text-xs);">
                            <strong>小尺寸 (Small):</strong> 紧凑布局使用
                        </li>
                        <li style="margin: var(--space-2) 0; padding: var(--space-2-5); background: var(--gray-100); border-radius: var(--radius); font-size: var(--text-sm);">
                            <strong>中等尺寸 (Medium):</strong> 默认标准尺寸
                        </li>
                        <li style="margin: var(--space-2) 0; padding: var(--space-4); background: var(--gray-100); border-radius: var(--radius-lg); font-size: var(--text-base);">
                            <strong>大尺寸 (Large):</strong> 突出显示使用
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 完善的表单组件系统 -->
        <div class="section">
            <h2>📝 表单组件系统 (Form Components)</h2>
            <p>统一的表单组件设计，包含所有状态和尺寸变体。</p>

            <h3>输入框组件 (Input Component)</h3>
            <div class="grid grid-cols-3 gap-6 mb-6">
                <div>
                    <h4>小尺寸 (Small)</h4>
                    <div class="mb-3">
                        <input type="text" class="lz-input lz-input--small" placeholder="小尺寸输入框">
                    </div>
                    <div class="mb-3">
                        <input type="text" class="lz-input lz-input--small lz-input--error" placeholder="错误状态" value="错误输入">
                    </div>
                    <div class="mb-3">
                        <input type="text" class="lz-input lz-input--small" placeholder="禁用状态" disabled>
                    </div>
                </div>
                <div>
                    <h4>中等尺寸 (Medium)</h4>
                    <div class="mb-3">
                        <input type="text" class="lz-input" placeholder="标准尺寸输入框">
                    </div>
                    <div class="mb-3">
                        <input type="text" class="lz-input lz-input--success" placeholder="成功状态" value="验证通过">
                    </div>
                    <div class="mb-3">
                        <input type="text" class="lz-input lz-input--loading" placeholder="加载状态">
                    </div>
                </div>
                <div>
                    <h4>大尺寸 (Large)</h4>
                    <div class="mb-3">
                        <input type="text" class="lz-input lz-input--large" placeholder="大尺寸输入框">
                    </div>
                    <div class="mb-3">
                        <input type="text" class="lz-input lz-input--large lz-input--warning" placeholder="警告状态" value="需要注意">
                    </div>
                    <div class="mb-3">
                        <input type="text" class="lz-input lz-input--large" placeholder="聚焦状态" style="border-color: var(--primary-500); box-shadow: 0 0 0 3px rgba(15, 213, 157, 0.1);">
                    </div>
                </div>
            </div>

            <h3>下拉选择器 (Select Component)</h3>
            <div class="grid grid-cols-3 gap-6 mb-6">
                <div>
                    <h4>小尺寸</h4>
                    <div class="mb-3">
                        <div class="lz-select lz-select--small" data-select="basic-small">
                            <div class="lz-select__trigger" tabindex="0">
                                <span class="lz-select__value lz-select__placeholder">请选择选项</span>
                                <span class="lz-select__arrow">▼</span>
                            </div>
                            <div class="lz-select__dropdown">
                                <ul class="lz-select__options">
                                    <li class="lz-select__option" data-value="option1">选项1</li>
                                    <li class="lz-select__option" data-value="option2">选项2</li>
                                    <li class="lz-select__option" data-value="option3">选项3</li>
                                    <li class="lz-select__option lz-select__option--disabled" data-value="option4">禁用选项</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <h4>中等尺寸</h4>
                    <div class="mb-3">
                        <div class="lz-select" data-select="basic-medium">
                            <div class="lz-select__trigger" tabindex="0">
                                <span class="lz-select__value lz-select__placeholder">请选择选项</span>
                                <span class="lz-select__arrow">▼</span>
                            </div>
                            <div class="lz-select__dropdown">
                                <ul class="lz-select__options">
                                    <li class="lz-select__option" data-value="frontend">前端开发</li>
                                    <li class="lz-select__option" data-value="backend">后端开发</li>
                                    <li class="lz-select__option" data-value="design">UI设计</li>
                                    <li class="lz-select__option" data-value="product">产品经理</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <h4>大尺寸</h4>
                    <div class="mb-3">
                        <div class="lz-select lz-select--large" data-select="basic-large">
                            <div class="lz-select__trigger" tabindex="0">
                                <span class="lz-select__value lz-select__placeholder">请选择选项</span>
                                <span class="lz-select__arrow">▼</span>
                            </div>
                            <div class="lz-select__dropdown">
                                <ul class="lz-select__options">
                                    <li class="lz-select__option" data-value="beijing">北京</li>
                                    <li class="lz-select__option" data-value="shanghai">上海</li>
                                    <li class="lz-select__option" data-value="guangzhou">广州</li>
                                    <li class="lz-select__option" data-value="shenzhen">深圳</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <h3>高级功能展示</h3>
            <div class="grid grid-cols-2 gap-6 mb-6">
                <div>
                    <h4>带搜索功能</h4>
                    <div class="mb-3">
                        <div class="lz-select" data-select="searchable" data-searchable="true">
                            <div class="lz-select__trigger" tabindex="0">
                                <span class="lz-select__value lz-select__placeholder">搜索并选择城市</span>
                                <span class="lz-select__arrow">▼</span>
                            </div>
                            <div class="lz-select__dropdown">
                                <div class="lz-select__search">
                                    <input type="text" class="lz-select__search-input" placeholder="搜索城市...">
                                </div>
                                <ul class="lz-select__options">
                                    <li class="lz-select__option" data-value="beijing" data-keywords="北京 beijing bj">北京</li>
                                    <li class="lz-select__option" data-value="shanghai" data-keywords="上海 shanghai sh">上海</li>
                                    <li class="lz-select__option" data-value="guangzhou" data-keywords="广州 guangzhou gz">广州</li>
                                    <li class="lz-select__option" data-value="shenzhen" data-keywords="深圳 shenzhen sz">深圳</li>
                                    <li class="lz-select__option" data-value="hangzhou" data-keywords="杭州 hangzhou hz">杭州</li>
                                    <li class="lz-select__option" data-value="nanjing" data-keywords="南京 nanjing nj">南京</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <h4>带图标和描述</h4>
                    <div class="mb-3">
                        <div class="lz-select" data-select="with-icons">
                            <div class="lz-select__trigger" tabindex="0">
                                <span class="lz-select__value lz-select__placeholder">选择编程语言</span>
                                <span class="lz-select__arrow">▼</span>
                            </div>
                            <div class="lz-select__dropdown">
                                <ul class="lz-select__options">
                                    <li class="lz-select__option" data-value="javascript">
                                        <span class="lz-select__option-icon">🟨</span>
                                        <div class="lz-select__option-text">
                                            JavaScript
                                            <div class="lz-select__option-description">动态编程语言</div>
                                        </div>
                                    </li>
                                    <li class="lz-select__option" data-value="python">
                                        <span class="lz-select__option-icon">🐍</span>
                                        <div class="lz-select__option-text">
                                            Python
                                            <div class="lz-select__option-description">简洁优雅的语言</div>
                                        </div>
                                    </li>
                                    <li class="lz-select__option" data-value="java">
                                        <span class="lz-select__option-icon">☕</span>
                                        <div class="lz-select__option-text">
                                            Java
                                            <div class="lz-select__option-description">企业级开发语言</div>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <h3>分组选项</h3>
            <div class="grid grid-cols-2 gap-6 mb-6">
                <div>
                    <h4>技能分组</h4>
                    <div class="mb-3">
                        <div class="lz-select" data-select="grouped">
                            <div class="lz-select__trigger" tabindex="0">
                                <span class="lz-select__value lz-select__placeholder">选择技能</span>
                                <span class="lz-select__arrow">▼</span>
                            </div>
                            <div class="lz-select__dropdown">
                                <ul class="lz-select__options">
                                    <li class="lz-select__group">前端技术</li>
                                    <li class="lz-select__option" data-value="vue">Vue.js</li>
                                    <li class="lz-select__option" data-value="react">React</li>
                                    <li class="lz-select__option" data-value="angular">Angular</li>
                                    <li class="lz-select__group">后端技术</li>
                                    <li class="lz-select__option" data-value="nodejs">Node.js</li>
                                    <li class="lz-select__option" data-value="python">Python</li>
                                    <li class="lz-select__option" data-value="java">Java</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <h4>多选模式</h4>
                    <div class="mb-3">
                        <div class="lz-select lz-select--multiple" data-select="multiple" data-multiple="true">
                            <div class="lz-select__trigger" tabindex="0">
                                <span class="lz-select__value lz-select__placeholder">选择多个标签</span>
                                <span class="lz-select__arrow">▼</span>
                            </div>
                            <div class="lz-select__dropdown">
                                <ul class="lz-select__options">
                                    <li class="lz-select__option" data-value="html">HTML</li>
                                    <li class="lz-select__option" data-value="css">CSS</li>
                                    <li class="lz-select__option" data-value="javascript">JavaScript</li>
                                    <li class="lz-select__option" data-value="vue">Vue.js</li>
                                    <li class="lz-select__option" data-value="react">React</li>
                                    <li class="lz-select__option" data-value="nodejs">Node.js</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <h3>状态展示</h3>
            <div class="grid grid-cols-3 gap-6 mb-6">
                <div>
                    <h4>错误状态</h4>
                    <div class="mb-3">
                        <div class="lz-select lz-select--error" data-select="error-state">
                            <div class="lz-select__trigger" tabindex="0">
                                <span class="lz-select__value">错误的选择</span>
                                <span class="lz-select__arrow">▼</span>
                            </div>
                            <div class="lz-select__dropdown">
                                <ul class="lz-select__options">
                                    <li class="lz-select__option" data-value="option1">选项1</li>
                                    <li class="lz-select__option" data-value="option2">选项2</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <h4>成功状态</h4>
                    <div class="mb-3">
                        <div class="lz-select lz-select--success" data-select="success-state">
                            <div class="lz-select__trigger" tabindex="0">
                                <span class="lz-select__value">正确的选择</span>
                                <span class="lz-select__arrow">▼</span>
                            </div>
                            <div class="lz-select__dropdown">
                                <ul class="lz-select__options">
                                    <li class="lz-select__option" data-value="option1">选项1</li>
                                    <li class="lz-select__option" data-value="option2">选项2</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <h4>禁用状态</h4>
                    <div class="mb-3">
                        <div class="lz-select" data-select="disabled-state">
                            <div class="lz-select__trigger lz-select__trigger--disabled" tabindex="-1">
                                <span class="lz-select__value lz-select__placeholder">禁用的选择器</span>
                                <span class="lz-select__arrow">▼</span>
                            </div>
                            <div class="lz-select__dropdown">
                                <ul class="lz-select__options">
                                    <li class="lz-select__option" data-value="option1">选项1</li>
                                    <li class="lz-select__option" data-value="option2">选项2</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <h3>文本域组件 (Textarea Component)</h3>
            <div class="grid grid-cols-2 gap-6 mb-6">
                <div>
                    <h4>标准文本域</h4>
                    <textarea class="lz-textarea" rows="4" placeholder="请输入多行文本内容..."></textarea>
                </div>
                <div>
                    <h4>错误状态文本域</h4>
                    <textarea class="lz-textarea lz-textarea--error" rows="4" placeholder="错误状态的文本域">输入内容有误</textarea>
                </div>
            </div>
        </div>

        <!-- 按钮组件系统 -->
        <div class="section">
            <h2>🔘 按钮组件系统 (Button Components)</h2>
            <p>完整的按钮组件，支持多种类型、尺寸和状态。</p>

            <h3>按钮类型 (Button Types)</h3>
            <div class="flex flex-wrap gap-3 mb-6">
                <button class="lz-button lz-button--primary">主要按钮</button>
                <button class="lz-button lz-button--secondary">次要按钮</button>
                <button class="lz-button lz-button--outline">轮廓按钮</button>
                <button class="lz-button lz-button--ghost">幽灵按钮</button>
                <button class="lz-button lz-button--danger">危险按钮</button>
            </div>

            <h3>按钮尺寸 (Button Sizes)</h3>
            <div class="flex flex-wrap items-center gap-3 mb-6">
                <button class="lz-button lz-button--primary lz-button--small">小按钮</button>
                <button class="lz-button lz-button--primary">标准按钮</button>
                <button class="lz-button lz-button--primary lz-button--large">大按钮</button>
            </div>

            <h3>按钮状态 (Button States)</h3>
            <div class="flex flex-wrap gap-3 mb-6">
                <button class="lz-button lz-button--primary">正常状态</button>
                <button class="lz-button lz-button--primary" disabled>禁用状态</button>
                <button class="lz-button lz-button--primary">
                    <span style="width: 16px; height: 16px; border: 2px solid white; border-top: 2px solid transparent; border-radius: 50%; animation: spin 1s linear infinite;"></span>
                    加载中
                </button>
            </div>
        </div>

        <!-- 导航组件系统 -->
        <div class="section">
            <h2>🧭 导航组件系统 (Navigation Components)</h2>

            <h3>面包屑导航 (Breadcrumb)</h3>
            <div class="mb-6">
                <nav class="lz-breadcrumb">
                    <ol class="lz-breadcrumb__list">
                        <li class="lz-breadcrumb__item">
                            <a href="#" class="lz-breadcrumb__link">首页</a>
                        </li>
                        <li class="lz-breadcrumb__item">
                            <a href="#" class="lz-breadcrumb__link">产品中心</a>
                        </li>
                        <li class="lz-breadcrumb__item">
                            <a href="#" class="lz-breadcrumb__link">电子产品</a>
                        </li>
                        <li class="lz-breadcrumb__item lz-breadcrumb__item--current">
                            <span class="lz-breadcrumb__text">智能手机</span>
                        </li>
                    </ol>
                </nav>
            </div>

            <h3>分页器 (Pagination)</h3>
            <div class="mb-6">
                <nav class="lz-pagination">
                    <button class="lz-pagination__button lz-pagination__button--prev" disabled>
                        <span>‹</span>
                        上一页
                    </button>
                    <div class="lz-pagination__pages">
                        <button class="lz-pagination__page lz-pagination__page--current">1</button>
                        <button class="lz-pagination__page">2</button>
                        <button class="lz-pagination__page">3</button>
                        <span class="lz-pagination__ellipsis">...</span>
                        <button class="lz-pagination__page">10</button>
                    </div>
                    <button class="lz-pagination__button lz-pagination__button--next">
                        下一页
                        <span>›</span>
                    </button>
                </nav>
            </div>

            <h3>步骤条 (Steps)</h3>
            <div class="mb-6">
                <div class="lz-steps">
                    <div class="lz-step lz-step--completed">
                        <div class="lz-step__icon">
                            <span>✓</span>
                        </div>
                        <div class="lz-step__content">
                            <div class="lz-step__title">填写信息</div>
                            <div class="lz-step__description">完善个人基本信息</div>
                        </div>
                    </div>
                    <div class="lz-step lz-step--current">
                        <div class="lz-step__icon">
                            <span>2</span>
                        </div>
                        <div class="lz-step__content">
                            <div class="lz-step__title">验证身份</div>
                            <div class="lz-step__description">上传身份证件进行验证</div>
                        </div>
                    </div>
                    <div class="lz-step">
                        <div class="lz-step__icon">
                            <span>3</span>
                        </div>
                        <div class="lz-step__content">
                            <div class="lz-step__title">完成注册</div>
                            <div class="lz-step__description">设置密码并完成注册</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据展示组件 -->
        <div class="section">
            <h2>📊 数据展示组件 (Data Display Components)</h2>

            <h3>表格组件 (Table)</h3>
            <div class="mb-6">
                <div class="lz-table-container">
                    <table class="lz-table">
                        <thead class="lz-table__header">
                            <tr>
                                <th class="lz-table__cell lz-table__cell--header">姓名</th>
                                <th class="lz-table__cell lz-table__cell--header">职位</th>
                                <th class="lz-table__cell lz-table__cell--header">部门</th>
                                <th class="lz-table__cell lz-table__cell--header">状态</th>
                                <th class="lz-table__cell lz-table__cell--header">操作</th>
                            </tr>
                        </thead>
                        <tbody class="lz-table__body">
                            <tr class="lz-table__row">
                                <td class="lz-table__cell">张三</td>
                                <td class="lz-table__cell">前端工程师</td>
                                <td class="lz-table__cell">技术部</td>
                                <td class="lz-table__cell">
                                    <span class="lz-badge lz-badge--success">在职</span>
                                </td>
                                <td class="lz-table__cell">
                                    <button class="lz-button lz-button--small lz-button--outline">编辑</button>
                                    <button class="lz-button lz-button--small lz-button--danger ml-2">删除</button>
                                </td>
                            </tr>
                            <tr class="lz-table__row">
                                <td class="lz-table__cell">李四</td>
                                <td class="lz-table__cell">UI设计师</td>
                                <td class="lz-table__cell">设计部</td>
                                <td class="lz-table__cell">
                                    <span class="lz-badge lz-badge--warning">请假</span>
                                </td>
                                <td class="lz-table__cell">
                                    <button class="lz-button lz-button--small lz-button--outline">编辑</button>
                                    <button class="lz-button lz-button--small lz-button--danger ml-2">删除</button>
                                </td>
                            </tr>
                            <tr class="lz-table__row">
                                <td class="lz-table__cell">王五</td>
                                <td class="lz-table__cell">产品经理</td>
                                <td class="lz-table__cell">产品部</td>
                                <td class="lz-table__cell">
                                    <span class="lz-badge lz-badge--error">离职</span>
                                </td>
                                <td class="lz-table__cell">
                                    <button class="lz-button lz-button--small lz-button--outline">编辑</button>
                                    <button class="lz-button lz-button--small lz-button--danger ml-2">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <h3>标签页组件 (Tabs)</h3>
            <div class="mb-6">
                <div class="lz-tabs">
                    <div class="lz-tabs__nav">
                        <button class="lz-tabs__tab lz-tabs__tab--active">基本信息</button>
                        <button class="lz-tabs__tab">联系方式</button>
                        <button class="lz-tabs__tab">工作经历</button>
                        <button class="lz-tabs__tab">项目经验</button>
                    </div>
                    <div class="lz-tabs__content">
                        <div class="lz-tabs__panel lz-tabs__panel--active">
                            <h4>基本信息</h4>
                            <p>这里显示用户的基本信息内容，包括姓名、年龄、性别等个人基础资料。</p>
                            <div class="grid grid-cols-2 gap-4 mt-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                                    <input type="text" class="lz-input" value="张三" readonly>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">年龄</label>
                                    <input type="text" class="lz-input" value="28" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <h3>折叠面板 (Collapse)</h3>
            <div class="mb-6">
                <div class="lz-collapse">
                    <div class="lz-collapse__item">
                        <div class="lz-collapse__header">
                            <h4 class="lz-collapse__title">产品介绍</h4>
                            <span class="lz-collapse__icon">▼</span>
                        </div>
                        <div class="lz-collapse__content lz-collapse__content--expanded">
                            <p>我们的产品是一款创新的解决方案，旨在帮助企业提高工作效率和用户体验。通过先进的技术和直观的界面设计，为用户提供最佳的使用体验。</p>
                        </div>
                    </div>
                    <div class="lz-collapse__item">
                        <div class="lz-collapse__header">
                            <h4 class="lz-collapse__title">技术特性</h4>
                            <span class="lz-collapse__icon">▶</span>
                        </div>
                        <div class="lz-collapse__content">
                            <ul>
                                <li>响应式设计，支持多端适配</li>
                                <li>模块化架构，易于扩展和维护</li>
                                <li>高性能优化，快速加载和响应</li>
                                <li>安全可靠，数据加密传输</li>
                            </ul>
                        </div>
                    </div>
                    <div class="lz-collapse__item">
                        <div class="lz-collapse__header">
                            <h4 class="lz-collapse__title">服务支持</h4>
                            <span class="lz-collapse__icon">▶</span>
                        </div>
                        <div class="lz-collapse__content">
                            <p>我们提供7×24小时技术支持，包括在线客服、电话支持和邮件咨询。专业的技术团队随时为您解决使用过程中遇到的问题。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 反馈组件 -->
        <div class="section">
            <h2>💬 反馈组件 (Feedback Components)</h2>

            <h3>模态框 (Modal)</h3>
            <div class="mb-6">
                <button class="lz-button lz-button--primary" onclick="document.getElementById('demo-modal').style.display='flex'">
                    打开模态框
                </button>

                <div id="demo-modal" class="lz-modal" style="display: none;">
                    <div class="lz-modal__backdrop"></div>
                    <div class="lz-modal__container">
                        <div class="lz-modal__content">
                            <div class="lz-modal__header">
                                <h3 class="lz-modal__title">确认操作</h3>
                                <button class="lz-modal__close" onclick="document.getElementById('demo-modal').style.display='none'">×</button>
                            </div>
                            <div class="lz-modal__body">
                                <p>您确定要执行此操作吗？此操作不可撤销，请谨慎考虑。</p>
                            </div>
                            <div class="lz-modal__footer">
                                <button class="lz-button lz-button--ghost" onclick="document.getElementById('demo-modal').style.display='none'">取消</button>
                                <button class="lz-button lz-button--primary ml-2" onclick="document.getElementById('demo-modal').style.display='none'">确认</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <h3>通知提示 (Notification)</h3>
            <div class="mb-6">
                <div class="flex gap-3">
                    <button class="lz-button lz-button--primary lz-button--small" onclick="showNotification('success')">成功通知</button>
                    <button class="lz-button lz-button--secondary lz-button--small" onclick="showNotification('info')">信息通知</button>
                    <button class="lz-button lz-button--outline lz-button--small" onclick="showNotification('warning')">警告通知</button>
                    <button class="lz-button lz-button--danger lz-button--small" onclick="showNotification('error')">错误通知</button>
                </div>

                <div id="notification-container" class="lz-notification-container"></div>
            </div>

            <h3>消息提示 (Message)</h3>
            <div class="mb-6">
                <div class="lz-message lz-message--info">
                    <div class="lz-message__icon">ℹ️</div>
                    <div class="lz-message__content">
                        <div class="lz-message__title">提示信息</div>
                        <div class="lz-message__text">这是一条信息提示，用于向用户传达重要信息。</div>
                    </div>
                    <button class="lz-message__close">×</button>
                </div>

                <div class="lz-message lz-message--warning">
                    <div class="lz-message__icon">⚠️</div>
                    <div class="lz-message__content">
                        <div class="lz-message__title">警告信息</div>
                        <div class="lz-message__text">请注意，您的操作可能会影响系统正常运行。</div>
                    </div>
                    <button class="lz-message__close">×</button>
                </div>
            </div>
        </div>

        <!-- 交互组件 -->
        <div class="section">
            <h2>🎛️ 交互组件 (Interactive Components)</h2>

            <h3>开关组件 (Switch)</h3>
            <div class="mb-6">
                <div class="grid grid-cols-3 gap-6">
                    <div>
                        <h4>标准开关</h4>
                        <label class="lz-switch">
                            <input type="checkbox" checked>
                            <span class="lz-switch__slider"></span>
                            <span class="lz-switch__label">启用通知</span>
                        </label>
                    </div>
                    <div>
                        <h4>小尺寸开关</h4>
                        <label class="lz-switch lz-switch--small">
                            <input type="checkbox">
                            <span class="lz-switch__slider"></span>
                            <span class="lz-switch__label">自动保存</span>
                        </label>
                    </div>
                    <div>
                        <h4>禁用状态</h4>
                        <label class="lz-switch">
                            <input type="checkbox" disabled>
                            <span class="lz-switch__slider"></span>
                            <span class="lz-switch__label">维护模式</span>
                        </label>
                    </div>
                </div>
            </div>

            <h3>滑块组件 (Slider)</h3>
            <div class="mb-6">
                <div class="grid grid-cols-2 gap-6">
                    <div>
                        <h4>音量控制</h4>
                        <div class="lz-slider">
                            <input type="range" class="lz-slider__input" min="0" max="100" value="60">
                            <div class="lz-slider__track">
                                <div class="lz-slider__progress" style="width: 60%;"></div>
                                <div class="lz-slider__thumb" style="left: 60%;"></div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4>亮度调节</h4>
                        <div class="lz-slider">
                            <input type="range" class="lz-slider__input" min="0" max="100" value="80">
                            <div class="lz-slider__track">
                                <div class="lz-slider__progress" style="width: 80%;"></div>
                                <div class="lz-slider__thumb" style="left: 80%;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <h3>评分组件 (Rating)</h3>
            <div class="mb-6">
                <div class="grid grid-cols-2 gap-6">
                    <div>
                        <h4>产品评分</h4>
                        <div class="lz-rating">
                            <span class="lz-rating__star lz-rating__star--filled">★</span>
                            <span class="lz-rating__star lz-rating__star--filled">★</span>
                            <span class="lz-rating__star lz-rating__star--filled">★</span>
                            <span class="lz-rating__star lz-rating__star--filled">★</span>
                            <span class="lz-rating__star">★</span>
                            <span class="lz-rating__text">4.0 分</span>
                        </div>
                    </div>
                    <div>
                        <h4>服务评分</h4>
                        <div class="lz-rating lz-rating--small">
                            <span class="lz-rating__star lz-rating__star--filled">★</span>
                            <span class="lz-rating__star lz-rating__star--filled">★</span>
                            <span class="lz-rating__star lz-rating__star--filled">★</span>
                            <span class="lz-rating__star lz-rating__star--half">★</span>
                            <span class="lz-rating__star">★</span>
                            <span class="lz-rating__text">3.5 分</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- CSS类名规范和使用指南 -->
        <div class="section">
            <h2>📚 CSS类名规范与使用指南</h2>

            <h3>命名约定 (Naming Convention)</h3>
            <div class="mb-6">
                <div style="background-color: var(--gray-900); color: var(--gray-100); padding: var(--space-4); border-radius: var(--radius-lg); font-family: 'Courier New', monospace; font-size: var(--text-sm);">
<pre style="margin: 0; white-space: pre-wrap;">/* 组件基础类名 */
.lz-{component}                    /* 组件根类 */
.lz-{component}__element           /* 组件元素类 */
.lz-{component}--modifier          /* 组件修饰符类 */
.lz-{component}__element--modifier /* 元素修饰符类 */

/* 示例 */
.lz-button                         /* 按钮组件 */
.lz-button__icon                   /* 按钮图标元素 */
.lz-button--primary                /* 主要按钮修饰符 */
.lz-button--large                  /* 大尺寸按钮修饰符 */
.lz-button__icon--left             /* 左侧图标修饰符 */</pre>
                </div>
            </div>

            <h3>组件状态类 (State Classes)</h3>
            <div class="grid grid-cols-2 gap-6 mb-6">
                <div>
                    <h4>通用状态</h4>
                    <ul style="font-family: 'Courier New', monospace; font-size: var(--text-sm);">
                        <li><code>--active</code> - 激活状态</li>
                        <li><code>--disabled</code> - 禁用状态</li>
                        <li><code>--loading</code> - 加载状态</li>
                        <li><code>--current</code> - 当前状态</li>
                        <li><code>--expanded</code> - 展开状态</li>
                        <li><code>--collapsed</code> - 收起状态</li>
                    </ul>
                </div>
                <div>
                    <h4>验证状态</h4>
                    <ul style="font-family: 'Courier New', monospace; font-size: var(--text-sm);">
                        <li><code>--success</code> - 成功状态</li>
                        <li><code>--warning</code> - 警告状态</li>
                        <li><code>--error</code> - 错误状态</li>
                        <li><code>--info</code> - 信息状态</li>
                    </ul>
                </div>
            </div>

            <h3>尺寸修饰符 (Size Modifiers)</h3>
            <div class="mb-6">
                <ul style="font-family: 'Courier New', monospace; font-size: var(--text-sm);">
                    <li><code>--small</code> - 小尺寸 (紧凑布局)</li>
                    <li><code>--medium</code> - 中等尺寸 (默认，可省略)</li>
                    <li><code>--large</code> - 大尺寸 (突出显示)</li>
                </ul>
            </div>

            <h3>主题定制 (Theme Customization)</h3>
            <div class="mb-6">
                <p>通过修改CSS变量来定制主题：</p>
                <div style="background-color: var(--gray-900); color: var(--gray-100); padding: var(--space-4); border-radius: var(--radius-lg); font-family: 'Courier New', monospace; font-size: var(--text-sm);">
<pre style="margin: 0; white-space: pre-wrap;">/* 自定义主题 */
:root {
  --primary: #your-primary-color;
  --primary-50: #your-primary-50;
  --primary-100: #your-primary-100;
  /* ... 其他颜色变量 */
}

/* 暗色主题 */
[data-theme="dark"] {
  --gray-50: #1a1a1a;
  --gray-900: #ffffff;
  /* ... 其他暗色变量 */
}</pre>
                </div>
            </div>
        </div>

        <!-- 总结和下一步 -->
        <div class="section text-center">
            <h2>🎉 设计系统总结</h2>
            <p style="font-size: var(--text-lg); color: var(--gray-600); margin-bottom: var(--space-6);">
                完整的Vue3组件库设计系统已经准备就绪！
            </p>

            <div class="grid grid-cols-3 gap-6 mb-6">
                <div class="p-6" style="background: var(--primary-50); border-radius: var(--radius-xl);">
                    <h3 style="color: var(--primary-600);">🎨 设计完整</h3>
                    <p style="font-size: var(--text-sm); color: var(--gray-600);">
                        包含20+组件的完整设计规范，覆盖表单、导航、数据展示、反馈等各个场景
                    </p>
                </div>
                <div class="p-6" style="background: var(--secondary-50); border-radius: var(--radius-xl);">
                    <h3 style="color: var(--secondary-600);">⚡ 开发友好</h3>
                    <p style="font-size: var(--text-sm); color: var(--gray-600);">
                        基于CSS变量的设计标记系统，BEM命名规范，易于维护和扩展
                    </p>
                </div>
                <div class="p-6" style="background: var(--accent-50); border-radius: var(--radius-xl);">
                    <h3 style="color: var(--accent-600);">📱 响应式</h3>
                    <p style="font-size: var(--text-sm); color: var(--gray-600);">
                        移动优先的响应式设计，支持多种屏幕尺寸和设备类型
                    </p>
                </div>
            </div>

            <div class="flex justify-center gap-4">
                <button class="lz-button lz-button--primary lz-button--large">开始开发</button>
                <button class="lz-button lz-button--outline lz-button--large">查看文档</button>
                <button class="lz-button lz-button--ghost lz-button--large">下载资源</button>
            </div>

            <p style="font-size: var(--text-sm); color: var(--gray-500); margin-top: var(--space-6);">
                Vue3组件库设计系统 v2.0 | 基于 #0FD59D 主色调 |
                <a href="#" style="color: var(--primary-600);">GitHub仓库</a> |
                <a href="#" style="color: var(--primary-600);">更新日志</a>
            </p>
        </div>
    </div>

    <script>
        // 自定义下拉选择器类
        class LzSelect {
            constructor(element) {
                this.element = element;
                this.trigger = element.querySelector('.lz-select__trigger');
                this.dropdown = element.querySelector('.lz-select__dropdown');
                this.valueElement = element.querySelector('.lz-select__value');
                this.arrow = element.querySelector('.lz-select__arrow');
                this.options = element.querySelectorAll('.lz-select__option:not(.lz-select__group)');
                this.searchInput = element.querySelector('.lz-select__search-input');

                this.isOpen = false;
                this.isDisabled = this.trigger.classList.contains('lz-select__trigger--disabled');
                this.isMultiple = element.dataset.multiple === 'true';
                this.isSearchable = element.dataset.searchable === 'true';
                this.selectedValues = [];
                this.focusedIndex = -1;
                this.originalOptions = Array.from(this.options);

                this.init();
            }

            init() {
                if (this.isDisabled) return;

                this.bindEvents();
                this.updateDisplay();
            }

            bindEvents() {
                // 点击触发器
                this.trigger.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.toggle();
                });

                // 键盘导航
                this.trigger.addEventListener('keydown', (e) => {
                    this.handleKeydown(e);
                });

                // 选项点击
                this.options.forEach((option, index) => {
                    option.addEventListener('click', (e) => {
                        e.stopPropagation();
                        if (!option.classList.contains('lz-select__option--disabled')) {
                            this.selectOption(option, index);
                        }
                    });
                });

                // 搜索功能
                if (this.searchInput) {
                    this.searchInput.addEventListener('input', (e) => {
                        this.filterOptions(e.target.value);
                    });

                    this.searchInput.addEventListener('keydown', (e) => {
                        if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                            e.preventDefault();
                            this.handleKeydown(e);
                        }
                    });
                }

                // 点击外部关闭
                document.addEventListener('click', (e) => {
                    if (!this.element.contains(e.target)) {
                        this.close();
                    }
                });

                // ESC键关闭
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && this.isOpen) {
                        this.close();
                        this.trigger.focus();
                    }
                });
            }

            toggle() {
                if (this.isOpen) {
                    this.close();
                } else {
                    this.open();
                }
            }

            open() {
                if (this.isDisabled) return;

                this.isOpen = true;
                this.dropdown.classList.add('lz-select__dropdown--open');
                this.arrow.classList.add('lz-select__arrow--open');
                this.trigger.classList.add('lz-select__trigger--focused');

                // 聚焦搜索框
                if (this.searchInput) {
                    setTimeout(() => {
                        this.searchInput.focus();
                    }, 100);
                }

                // 重置焦点索引
                this.focusedIndex = -1;
                this.updateFocusedOption();
            }

            close() {
                this.isOpen = false;
                this.dropdown.classList.remove('lz-select__dropdown--open');
                this.arrow.classList.remove('lz-select__arrow--open');
                this.trigger.classList.remove('lz-select__trigger--focused');

                // 清空搜索
                if (this.searchInput) {
                    this.searchInput.value = '';
                    this.filterOptions('');
                }

                this.focusedIndex = -1;
                this.updateFocusedOption();
            }

            selectOption(option, index) {
                const value = option.dataset.value;
                const text = option.textContent.trim();

                if (this.isMultiple) {
                    this.toggleMultipleSelection(value, text, option);
                } else {
                    this.selectedValues = [{ value, text }];
                    this.updateDisplay();
                    this.close();
                }

                // 触发change事件
                this.element.dispatchEvent(new CustomEvent('change', {
                    detail: {
                        value: this.isMultiple ? this.selectedValues.map(v => v.value) : value,
                        text: this.isMultiple ? this.selectedValues.map(v => v.text) : text
                    }
                }));
            }

            toggleMultipleSelection(value, text, option) {
                const existingIndex = this.selectedValues.findIndex(v => v.value === value);

                if (existingIndex > -1) {
                    // 取消选择
                    this.selectedValues.splice(existingIndex, 1);
                    option.classList.remove('lz-select__option--selected');
                    option.querySelector('.lz-select__option-check')?.remove();
                } else {
                    // 添加选择
                    this.selectedValues.push({ value, text });
                    option.classList.add('lz-select__option--selected');

                    // 添加选中标记
                    if (!option.querySelector('.lz-select__option-check')) {
                        const check = document.createElement('span');
                        check.className = 'lz-select__option-check';
                        check.textContent = '✓';
                        option.appendChild(check);
                    }
                }

                this.updateDisplay();
            }

            removeTag(value) {
                const index = this.selectedValues.findIndex(v => v.value === value);
                if (index > -1) {
                    this.selectedValues.splice(index, 1);

                    // 更新选项状态
                    const option = this.element.querySelector(`[data-value="${value}"]`);
                    if (option) {
                        option.classList.remove('lz-select__option--selected');
                        option.querySelector('.lz-select__option-check')?.remove();
                    }

                    this.updateDisplay();

                    // 触发change事件
                    this.element.dispatchEvent(new CustomEvent('change', {
                        detail: {
                            value: this.selectedValues.map(v => v.value),
                            text: this.selectedValues.map(v => v.text)
                        }
                    }));
                }
            }

            updateDisplay() {
                if (this.selectedValues.length === 0) {
                    this.valueElement.className = 'lz-select__value lz-select__placeholder';
                    if (this.isMultiple) {
                        this.valueElement.textContent = this.element.dataset.placeholder || '请选择';
                    } else {
                        this.valueElement.textContent = this.element.dataset.placeholder || '请选择选项';
                    }
                } else if (this.isMultiple) {
                    this.valueElement.className = 'lz-select__value';
                    this.renderTags();
                } else {
                    this.valueElement.className = 'lz-select__value';
                    this.valueElement.textContent = this.selectedValues[0].text;
                }

                // 更新选项选中状态
                this.options.forEach(option => {
                    const value = option.dataset.value;
                    const isSelected = this.selectedValues.some(v => v.value === value);

                    if (isSelected) {
                        option.classList.add('lz-select__option--selected');
                        if (this.isMultiple && !option.querySelector('.lz-select__option-check')) {
                            const check = document.createElement('span');
                            check.className = 'lz-select__option-check';
                            check.textContent = '✓';
                            option.appendChild(check);
                        }
                    } else {
                        option.classList.remove('lz-select__option--selected');
                        option.querySelector('.lz-select__option-check')?.remove();
                    }
                });
            }

            renderTags() {
                this.valueElement.innerHTML = '';

                if (this.selectedValues.length === 0) {
                    this.valueElement.textContent = this.element.dataset.placeholder || '请选择';
                    return;
                }

                this.selectedValues.forEach(({ value, text }) => {
                    const tag = document.createElement('span');
                    tag.className = 'lz-select__tag';
                    tag.innerHTML = `
                        <span>${text}</span>
                        <span class="lz-select__tag-close" data-value="${value}">×</span>
                    `;

                    // 绑定删除事件
                    const closeBtn = tag.querySelector('.lz-select__tag-close');
                    closeBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.removeTag(value);
                    });

                    this.valueElement.appendChild(tag);
                });
            }

            handleKeydown(e) {
                if (!this.isOpen && (e.key === 'ArrowDown' || e.key === 'ArrowUp' || e.key === 'Enter' || e.key === ' ')) {
                    e.preventDefault();
                    this.open();
                    return;
                }

                if (!this.isOpen) return;

                const visibleOptions = Array.from(this.options).filter(option =>
                    option.style.display !== 'none' && !option.classList.contains('lz-select__option--disabled')
                );

                switch (e.key) {
                    case 'ArrowDown':
                        e.preventDefault();
                        this.focusedIndex = Math.min(this.focusedIndex + 1, visibleOptions.length - 1);
                        this.updateFocusedOption();
                        break;
                    case 'ArrowUp':
                        e.preventDefault();
                        this.focusedIndex = Math.max(this.focusedIndex - 1, 0);
                        this.updateFocusedOption();
                        break;
                    case 'Enter':
                        e.preventDefault();
                        if (this.focusedIndex >= 0 && visibleOptions[this.focusedIndex]) {
                            const option = visibleOptions[this.focusedIndex];
                            const originalIndex = Array.from(this.options).indexOf(option);
                            this.selectOption(option, originalIndex);
                        }
                        break;
                    case 'Escape':
                        e.preventDefault();
                        this.close();
                        break;
                }
            }

            updateFocusedOption() {
                const visibleOptions = Array.from(this.options).filter(option =>
                    option.style.display !== 'none' && !option.classList.contains('lz-select__option--disabled')
                );

                // 移除所有焦点状态
                this.options.forEach(option => {
                    option.classList.remove('lz-select__option--focused');
                });

                // 添加当前焦点状态
                if (this.focusedIndex >= 0 && visibleOptions[this.focusedIndex]) {
                    visibleOptions[this.focusedIndex].classList.add('lz-select__option--focused');

                    // 滚动到可见区域
                    visibleOptions[this.focusedIndex].scrollIntoView({
                        block: 'nearest',
                        behavior: 'smooth'
                    });
                }
            }

            filterOptions(searchTerm) {
                const term = searchTerm.toLowerCase().trim();
                let hasVisibleOptions = false;

                this.options.forEach(option => {
                    const text = option.textContent.toLowerCase();
                    const keywords = option.dataset.keywords?.toLowerCase() || '';
                    const isMatch = text.includes(term) || keywords.includes(term);

                    if (isMatch || term === '') {
                        option.style.display = '';
                        hasVisibleOptions = true;
                    } else {
                        option.style.display = 'none';
                    }
                });

                // 显示/隐藏空状态
                let emptyState = this.dropdown.querySelector('.lz-select__empty');
                if (!hasVisibleOptions && term !== '') {
                    if (!emptyState) {
                        emptyState = document.createElement('div');
                        emptyState.className = 'lz-select__empty';
                        emptyState.textContent = '无匹配选项';
                        this.dropdown.appendChild(emptyState);
                    }
                    emptyState.style.display = 'block';
                } else if (emptyState) {
                    emptyState.style.display = 'none';
                }

                // 重置焦点索引
                this.focusedIndex = -1;
                this.updateFocusedOption();
            }
        }

        // 初始化所有下拉选择器
        document.addEventListener('DOMContentLoaded', function() {
            const selects = document.querySelectorAll('.lz-select');
            selects.forEach(select => {
                new LzSelect(select);
            });
        });

        // 通知功能
        function showNotification(type) {
            const container = document.getElementById('notification-container');
            const notification = document.createElement('div');

            const config = {
                success: { icon: '✅', title: '操作成功', text: '您的操作已成功完成！' },
                info: { icon: 'ℹ️', title: '信息提示', text: '这是一条信息通知。' },
                warning: { icon: '⚠️', title: '警告提示', text: '请注意相关事项。' },
                error: { icon: '❌', title: '操作失败', text: '操作失败，请重试。' }
            };

            const { icon, title, text } = config[type];

            notification.className = `lz-notification lz-notification--${type}`;
            notification.innerHTML = `
                <div class="lz-notification__icon">${icon}</div>
                <div class="lz-notification__content">
                    <div class="lz-notification__title">${title}</div>
                    <div class="lz-notification__text">${text}</div>
                </div>
                <button class="lz-notification__close" onclick="this.parentElement.remove()">×</button>
            `;

            container.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 3000);
        }

        // 消息提示关闭功能
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('lz-message__close')) {
                e.target.closest('.lz-message').remove();
            }
        });

        // 模态框背景点击关闭
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('lz-modal__backdrop')) {
                e.target.closest('.lz-modal').style.display = 'none';
            }
        });

        // 滑块交互
        document.querySelectorAll('.lz-slider__input').forEach(slider => {
            slider.addEventListener('input', function() {
                const value = this.value;
                const max = this.max;
                const percentage = (value / max) * 100;

                const progress = this.parentElement.querySelector('.lz-slider__progress');
                const thumb = this.parentElement.querySelector('.lz-slider__thumb');

                if (progress) progress.style.width = percentage + '%';
                if (thumb) thumb.style.left = percentage + '%';
            });
        });

        // 评分交互
        document.querySelectorAll('.lz-rating').forEach(rating => {
            const stars = rating.querySelectorAll('.lz-rating__star');

            stars.forEach((star, index) => {
                star.addEventListener('click', function() {
                    stars.forEach((s, i) => {
                        if (i <= index) {
                            s.classList.add('lz-rating__star--filled');
                            s.classList.remove('lz-rating__star--half');
                        } else {
                            s.classList.remove('lz-rating__star--filled', 'lz-rating__star--half');
                        }
                    });

                    const text = rating.querySelector('.lz-rating__text');
                    if (text) {
                        text.textContent = `${index + 1}.0 分`;
                    }
                });
            });
        });

        // 标签页切换
        document.querySelectorAll('.lz-tabs__tab').forEach(tab => {
            tab.addEventListener('click', function() {
                const tabs = this.parentElement;
                const tabsContainer = tabs.parentElement;

                // 移除所有活动状态
                tabs.querySelectorAll('.lz-tabs__tab').forEach(t => t.classList.remove('lz-tabs__tab--active'));
                tabsContainer.querySelectorAll('.lz-tabs__panel').forEach(p => p.classList.remove('lz-tabs__panel--active'));

                // 添加当前活动状态
                this.classList.add('lz-tabs__tab--active');

                // 这里可以添加面板切换逻辑
                const firstPanel = tabsContainer.querySelector('.lz-tabs__panel');
                if (firstPanel) {
                    firstPanel.classList.add('lz-tabs__panel--active');
                }
            });
        });

        // 折叠面板切换
        document.querySelectorAll('.lz-collapse__header').forEach(header => {
            header.addEventListener('click', function() {
                const content = this.nextElementSibling;
                const icon = this.querySelector('.lz-collapse__icon');

                if (content.classList.contains('lz-collapse__content--expanded')) {
                    content.classList.remove('lz-collapse__content--expanded');
                    icon.textContent = '▶';
                } else {
                    content.classList.add('lz-collapse__content--expanded');
                    icon.textContent = '▼';
                }
            });
        });
    </script>
</body>
</html>
