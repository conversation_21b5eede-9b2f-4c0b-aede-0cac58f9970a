{"name": "lanzt-ui", "version": "1.0.0", "description": "A modern Vue 3 component library based on #0FD59D design system", "type": "module", "main": "lib/index.js", "module": "es/index.js", "types": "lib/index.d.ts", "exports": {".": {"import": "./es/index.js", "require": "./lib/index.js", "types": "./lib/index.d.ts"}, "./es": "./es/index.js", "./lib": "./lib/index.js", "./theme": "./theme/index.css", "./package.json": "./package.json"}, "files": ["lib", "es", "theme", "README.md"], "scripts": {"dev": "pnpm run -C playground dev", "build": "pnpm run build:components && pnpm run build:theme", "build:components": "vite build --config build/vite.config.ts", "build:theme": "vite build --config build/vite.theme.config.ts", "build:docs": "pnpm run -C docs build", "docs:dev": "pnpm run -C docs dev", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix", "format": "prettier --write .", "typecheck": "vue-tsc --noEmit", "prepare": "husky install", "release": "bumpp && npm publish", "clean": "rimraf lib es dist"}, "keywords": ["vue", "vue3", "component", "ui", "library", "typescript", "vite"], "author": "LanZT", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/lanzt/lanzt-ui.git"}, "bugs": {"url": "https://github.com/lanzt/lanzt-ui/issues"}, "homepage": "https://github.com/lanzt/lanzt-ui#readme", "peerDependencies": {"vue": "^3.3.0"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@mdi/font": "^7.4.47", "@types/node": "^20.10.4", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "@vitejs/plugin-vue": "^4.5.2", "@vitest/ui": "^1.0.4", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.3", "bumpp": "^9.2.0", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "happy-dom": "^12.10.3", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.0", "rimraf": "^5.0.5", "sass": "^1.69.5", "typescript": "^5.3.3", "unplugin-vue-components": "^0.25.2", "vite": "^5.0.8", "vite-plugin-dts": "^3.6.4", "vitest": "^1.0.4", "vue": "^3.3.8", "vue-tsc": "^1.8.25"}, "lint-staged": {"*.{vue,js,ts,jsx,tsx}": ["eslint --fix", "prettier --write"], "*.{css,scss,html,md}": ["prettier --write"]}, "engines": {"node": ">=16.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.12.0"}