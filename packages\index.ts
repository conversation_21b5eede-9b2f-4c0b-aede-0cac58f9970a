import type { App } from 'vue'

// 组件导入
import LzButton from './button'
import LzInput from './input'
import LzSelect from './select'
import LzTextarea from './textarea'
import LzSwitch from './switch'
import LzSlider from './slider'
import LzRating from './rating'
import LzTable from './table'
import LzTabs from './tabs'
import LzBadge from './badge'
import LzCollapse from './collapse'
import LzBreadcrumb from './breadcrumb'
import LzPagination from './pagination'
import LzSteps from './steps'
import LzModal from './modal'
import LzMessage from './message'
import LzNotification from './notification'
import LzIcon from './icon'

// 所有组件列表
const components = [
  LzButton,
  LzInput,
  LzSelect,
  LzTextarea,
  LzSwitch,
  LzSlider,
  LzRating,
  LzTable,
  LzTabs,
  LzBadge,
  LzCollapse,
  LzBreadcrumb,
  LzPagination,
  LzSteps,
  LzModal,
  LzMessage,
  LzNotification,
  LzIcon,
]

// 定义安装函数
const install = (app: App): void => {
  components.forEach(component => {
    app.component(component.name, component)
  })
}

// 版本信息
const version = '1.0.0'

// 导出组件库
export {
  // 组件
  LzButton,
  LzInput,
  LzSelect,
  LzTextarea,
  LzSwitch,
  LzSlider,
  LzRating,
  LzTable,
  LzTabs,
  LzBadge,
  LzCollapse,
  LzBreadcrumb,
  LzPagination,
  LzSteps,
  LzModal,
  LzMessage,
  LzNotification,
  LzIcon,
  
  // 安装函数
  install,
  version,
}

// 默认导出
export default {
  install,
  version,
}

// 类型导出
export * from './button'
export * from './input'
export * from './select'
export * from './textarea'
export * from './switch'
export * from './slider'
export * from './rating'
export * from './table'
export * from './tabs'
export * from './badge'
export * from './collapse'
export * from './breadcrumb'
export * from './pagination'
export * from './steps'
export * from './modal'
export * from './message'
export * from './notification'
export * from './icon'
