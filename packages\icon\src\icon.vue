<template>
  <i
    ref="iconRef"
    :class="[
      'lz-icon',
      `mdi mdi-${name}`,
      {
        'lz-icon--spin': spin,
      },
    ]"
    :style="iconStyle"
  />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { iconProps } from './icon'
import type { IconInstance } from './instance'

defineOptions({
  name: 'LzIcon',
})

const props = defineProps(iconProps)

const iconRef = ref<HTMLElement>()

const iconStyle = computed(() => ({
  fontSize: props.size ? `${props.size}px` : undefined,
  color: props.color || undefined,
}))

defineExpose<IconInstance>({
  ref: iconRef,
})
</script>
