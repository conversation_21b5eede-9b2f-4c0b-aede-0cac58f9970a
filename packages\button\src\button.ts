import { buildProps, definePropType } from '../../utils'
import type { ExtractPropTypes } from 'vue'

export const buttonTypes = ['primary', 'secondary', 'outline', 'ghost', 'danger', 'text', 'link'] as const
export const buttonSizes = ['small', 'medium', 'large'] as const
export const buttonNativeTypes = ['button', 'submit', 'reset'] as const

export const buttonProps = buildProps({
  /**
   * @description 按钮类型
   */
  type: {
    type: definePropType<(typeof buttonTypes)[number]>(String),
    values: buttonTypes,
    default: 'primary',
  },
  /**
   * @description 按钮尺寸
   */
  size: {
    type: definePropType<(typeof buttonSizes)[number]>(String),
    values: buttonSizes,
    default: 'medium',
  },
  /**
   * @description 原生 type 属性
   */
  nativeType: {
    type: definePropType<(typeof buttonNativeTypes)[number]>(String),
    values: buttonNativeTypes,
    default: 'button',
  },
  /**
   * @description 是否禁用
   */
  disabled: Boolean,
  /**
   * @description 是否加载中
   */
  loading: Boolean,
  /**
   * @description 图标名称
   */
  icon: String,
  /**
   * @description 是否圆角
   */
  round: Boolean,
  /**
   * @description 是否圆形
   */
  circle: Boolean,
  /**
   * @description 是否文字按钮
   */
  text: Boolean,
  /**
   * @description 是否链接按钮
   */
  link: Boolean,
} as const)

export const buttonEmits = {
  click: (evt: MouseEvent) => evt instanceof MouseEvent,
}

export type ButtonProps = ExtractPropTypes<typeof buttonProps>
export type ButtonEmits = typeof buttonEmits
export type ButtonType = ButtonProps['type']
export type ButtonSize = ButtonProps['size']
export type ButtonNativeType = ButtonProps['nativeType']
