# LanZT UI

一个基于 Vue 3 + TypeScript 的现代化组件库，采用 #0FD59D 主色调设计系统。

## ✨ 特性

- 🎨 **现代化设计**：基于 #0FD59D 主色调的完整设计系统
- 🔧 **TypeScript**：完整的 TypeScript 支持
- 📱 **响应式**：移动端优先的响应式设计
- 🎯 **Tree Shaking**：支持按需引入，减小包体积
- 🌙 **主题定制**：基于 CSS 变量的主题系统
- ♿ **无障碍**：遵循 WAI-ARIA 标准
- 🧪 **测试覆盖**：完整的单元测试覆盖

## 📦 安装

```bash
# npm
npm install lanzt-ui

# yarn
yarn add lanzt-ui

# pnpm
pnpm add lanzt-ui
```

## 🚀 快速开始

### 完整引入

```typescript
import { createApp } from 'vue'
import LanztUI from 'lanzt-ui'
import 'lanzt-ui/theme/index.css'
import '@mdi/font/css/materialdesignicons.css'

const app = createApp(App)
app.use(LanztUI)
app.mount('#app')
```

### 按需引入

```typescript
import { createApp } from 'vue'
import { LzButton, LzInput } from 'lanzt-ui'
import 'lanzt-ui/theme/index.css'

const app = createApp(App)
app.component('LzButton', LzButton)
app.component('LzInput', LzInput)
app.mount('#app')
```

## 📚 组件

### 基础组件
- [x] Button 按钮
- [x] Icon 图标
- [ ] Link 链接

### 表单组件
- [x] Input 输入框
- [x] Select 选择器
- [x] Textarea 文本域
- [x] Switch 开关
- [x] Slider 滑块
- [x] Rating 评分
- [ ] Checkbox 复选框
- [ ] Radio 单选框
- [ ] Form 表单

### 数据展示
- [x] Table 表格
- [x] Tabs 标签页
- [x] Badge 徽章
- [x] Collapse 折叠面板
- [ ] Card 卡片
- [ ] Avatar 头像
- [ ] Tag 标签

### 导航组件
- [x] Breadcrumb 面包屑
- [x] Pagination 分页
- [x] Steps 步骤条
- [ ] Menu 菜单
- [ ] Dropdown 下拉菜单

### 反馈组件
- [x] Modal 模态框
- [x] Message 消息提示
- [x] Notification 通知
- [ ] Alert 警告提示
- [ ] Loading 加载
- [ ] Tooltip 文字提示

## 🎨 设计系统

### 颜色系统
- **主色调**：#0FD59D（青绿色）
- **辅助色**：#4F46E5（蓝紫色）
- **强调色**：#F59E0B（橙黄色）
- **状态色**：成功、警告、错误、信息

### 尺寸规范
- **小尺寸**：紧凑布局使用
- **中等尺寸**：默认标准尺寸
- **大尺寸**：突出显示使用

### 间距系统
基于 4px 基准的间距系统，提供 20 个间距层级。

## 🛠️ 开发

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建组件库
pnpm build

# 运行测试
pnpm test

# 代码检查
pnpm lint

# 格式化代码
pnpm format
```

## 📄 许可证

[MIT](./LICENSE) License © 2024 LanZT

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 联系

如有问题，请提交 [Issue](https://github.com/lanzt/lanzt-ui/issues)。
