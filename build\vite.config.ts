import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import dts from 'vite-plugin-dts'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    vue(),
    dts({
      insertTypesEntry: true,
      cleanVueFileName: true,
      skipDiagnostics: false,
      tsConfigFilePath: '../tsconfig.json',
      include: ['../packages/**/*'],
      exclude: ['../packages/**/style/**/*'],
    }),
  ],
  build: {
    lib: {
      entry: resolve(__dirname, '../packages/index.ts'),
      name: 'LanztUI',
      fileName: format => `index.${format}.js`,
      formats: ['es', 'umd'],
    },
    rollupOptions: {
      external: ['vue'],
      output: {
        globals: {
          vue: 'Vue',
        },
        exports: 'named',
      },
    },
    outDir: resolve(__dirname, '../lib'),
    emptyOutDir: true,
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, '../packages'),
    },
  },
})
