import { buildProps } from '../../utils'
import type { ExtractPropTypes } from 'vue'

export const iconProps = buildProps({
  /**
   * @description 图标名称 (Material Design Icons)
   */
  name: {
    type: String,
    required: true,
  },
  /**
   * @description 图标大小
   */
  size: {
    type: [String, Number],
  },
  /**
   * @description 图标颜色
   */
  color: String,
  /**
   * @description 是否旋转
   */
  spin: Boolean,
} as const)

export type IconProps = ExtractPropTypes<typeof iconProps>
