import type { PropType } from 'vue'

export const buildProps = <
  T extends Record<
    string,
    | { [K in 'type' | 'required' | 'values' | 'default' | 'validator']?: any }
    | PropType<any>
    | PropType<any>[]
    | (() => any)
    | ((new (...args: any[]) => any) | undefined)[]
  >
>(
  props: T
): T => props

export const definePropType = <T>(val: any): PropType<T> => val

export const isString = (val: any): val is string => typeof val === 'string'
export const isNumber = (val: any): val is number => typeof val === 'number'
export const isBoolean = (val: any): val is boolean => typeof val === 'boolean'
export const isFunction = (val: any): val is Function => typeof val === 'function'
export const isArray = Array.isArray
export const isObject = (val: any): val is Record<any, any> =>
  val !== null && typeof val === 'object'

export const isEmpty = (val: unknown) =>
  (!val && val !== 0) ||
  (isArray(val) && val.length === 0) ||
  (isObject(val) && Object.keys(val).length === 0)
