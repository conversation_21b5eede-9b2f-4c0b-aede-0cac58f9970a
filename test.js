/**
 * 解析 curl 命令，提取请求信息
 * @param {string} curlCommand - curl 命令字符串
 * @returns {Object} 解析后的请求信息
 */
function parseCurlCommand(curlCommand) {
    if (!curlCommand || typeof curlCommand !== 'string') {
        return {
            url: '',
            method: 'GET',
            headers: {},
            data: '',
            queryParams: {}
        };
    }

    const result = {
        url: '',
        method: 'GET',
        headers: {},
        data: '',
        queryParams: {}
    };

    // 清理命令，移除多余的空白字符和换行符
    const cleanCommand = curlCommand
        .replace(/\\\s*\n\s*/g, ' ')  // 处理换行连接符
        .replace(/\s+/g, ' ')         // 合并多个空格
        .trim();

    // 提取 URL - 支持多种格式
    let urlMatch = cleanCommand.match(/--url\s+['"]?([^'"\s]+)['"]?/);
    if (!urlMatch) {
        // 如果没有 --url 参数，尝试匹配 curl 后面直接跟的 URL
        urlMatch = cleanCommand.match(/curl\s+['"]?([^'"\s-]+)['"]?/);
    }

    if (urlMatch) {
        const fullUrl = urlMatch[1];
        try {
            const urlObj = new URL(fullUrl);
            result.url = `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`;

            // 提取查询参数
            urlObj.searchParams.forEach((value, key) => {
                result.queryParams[key] = value;
            });
        } catch (e) {
            result.url = fullUrl;
        }
    }

    // 提取请求方法
    const methodMatch = cleanCommand.match(/--request\s+(\w+)|--X\s+(\w+)|-X\s+(\w+)/i);
    if (methodMatch) {
        result.method = (methodMatch[1] || methodMatch[2] || methodMatch[3]).toUpperCase();
    }

    // 提取请求头
    const headerMatches = cleanCommand.matchAll(/--header\s+['"]([^'"]+)['"]|-H\s+['"]([^'"]+)['"]/g);
    for (const match of headerMatches) {
        const headerStr = match[1] || match[2];
        const colonIndex = headerStr.indexOf(':');
        if (colonIndex > 0) {
            const key = headerStr.substring(0, colonIndex).trim();
            const value = headerStr.substring(colonIndex + 1).trim();
            result.headers[key] = value;
        }
    }

    // 提取请求体数据
    // 使用更精确的方法来匹配引号内的内容，支持多行和嵌套引号

    // 尝试匹配 --data, --data-raw, -d 参数
    const dataPatterns = [
        /--data\s+/,
        /--data-raw\s+/,
        /-d\s+/
    ];

    for (const pattern of dataPatterns) {
        const match = cleanCommand.match(pattern);
        if (match) {
            const startIndex = match.index + match[0].length;
            const remainingCommand = cleanCommand.substring(startIndex);

            // 检查是否以引号开始
            if (remainingCommand.startsWith("'") || remainingCommand.startsWith('"')) {
                const quote = remainingCommand[0];
                let endIndex = 1;
                let escapeNext = false;

                // 查找匹配的结束引号
                while (endIndex < remainingCommand.length) {
                    const char = remainingCommand[endIndex];

                    if (escapeNext) {
                        escapeNext = false;
                    } else if (char === '\\') {
                        escapeNext = true;
                    } else if (char === quote) {
                        // 找到匹配的结束引号
                        result.data = remainingCommand.substring(1, endIndex);
                        break;
                    }
                    endIndex++;
                }
            } else {
                // 如果没有引号，匹配到下一个空格或命令结束
                const spaceIndex = remainingCommand.search(/\s+--/);
                if (spaceIndex > 0) {
                    result.data = remainingCommand.substring(0, spaceIndex).trim();
                } else {
                    result.data = remainingCommand.trim();
                }
            }
            break;
        }
    }

    return result;
}

const testdata = `curl --request POST \
  --url http://*************:8080/mtex/agent/api/park/detail \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Connection: keep-alive' \
  --header 'Content-Type: application/json' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --data '{
    "parkIdList": ["DF09A15D-F495-4641-B838-6EDACCBD2163"]
}'`

console.log('测试1 - 多行JSON数据:');
console.log(parseCurlCommand(testdata));

// 测试2: 单行数据
const testdata2 = `curl -X POST http://example.com --data '{"key":"value"}'`;
console.log('\n测试2 - 单行JSON数据:');
console.log(parseCurlCommand(testdata2));

// 测试3: 双引号数据
const testdata3 = `curl -X POST http://example.com --data "{\\"key\\":\\"value with 'quotes\\"}"`;
console.log('\n测试3 - 双引号包含转义引号:');
console.log(parseCurlCommand(testdata3));

// 测试4: 无引号数据
const testdata4 = `curl -X POST http://example.com --data simple-data`;
console.log('\n测试4 - 无引号数据:');
console.log(parseCurlCommand(testdata4));
