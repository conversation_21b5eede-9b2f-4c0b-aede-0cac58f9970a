# LanZT UI 开发路线图

## 🎯 项目概述

基于 #0FD59D 主色调的现代化 Vue 3 组件库，采用 TypeScript + Vite + SCSS 技术栈。

## ✅ 已完成工作

### 1. 项目工程化架构 ✅
- [x] Monorepo 项目结构
- [x] TypeScript 配置
- [x] Vite 构建配置
- [x] ESLint + Prettier 代码规范
- [x] Commitlint 提交规范
- [x] Vitest 测试框架
- [x] Husky Git Hooks

### 2. 设计系统基础 ✅
- [x] 完整的 CSS 变量系统 (170+ 设计标记)
- [x] BEM 命名规范
- [x] Material Design Icons 集成
- [x] 响应式设计支持
- [x] 暗色主题变量预设
- [x] 动画性能优化

### 3. 核心组件 ✅
- [x] Button 按钮组件 (完整实现)
- [x] Icon 图标组件 (Material Design Icons)
- [x] 自定义 Select 下拉选择器 (替换原生实现)

### 4. 设计系统展示 ✅
- [x] component-library.html 完整设计规范
- [x] 15+ 组件样式定义
- [x] 交互功能演示
- [x] 图标系统优化

## 🚧 开发任务清单

### Phase 1: 核心基础组件 (优先级: P0)

#### 1.1 表单组件
- [ ] **Checkbox 复选框** (预计 3-4 天)
  - [ ] 基础复选框组件
  - [ ] 复选框组 CheckboxGroup
  - [ ] 全选/半选状态
  - [ ] 禁用状态和验证
  - [ ] 单元测试

- [ ] **Radio 单选框** (预计 3-4 天)
  - [ ] 基础单选框组件
  - [ ] 单选框组 RadioGroup
  - [ ] 按钮样式单选
  - [ ] 禁用状态和验证
  - [ ] 单元测试

- [ ] **Form 表单系统** (预计 5-7 天)
  - [ ] Form 表单容器
  - [ ] FormItem 表单项
  - [ ] 表单验证规则
  - [ ] 表单布局 (水平/垂直)
  - [ ] 错误信息展示
  - [ ] 单元测试

#### 1.2 反馈组件
- [ ] **Loading 加载** (预计 2-3 天)
  - [ ] 页面级加载
  - [ ] 局部加载
  - [ ] 按钮加载状态
  - [ ] 自定义加载文案
  - [ ] 单元测试

### Phase 2: 常用组件 (优先级: P1)

#### 2.1 数据展示
- [ ] **Card 卡片** (预计 2-3 天)
  - [ ] 基础卡片
  - [ ] 带头部/底部的卡片
  - [ ] 可悬停卡片
  - [ ] 阴影变体
  - [ ] 单元测试

- [ ] **Avatar 头像** (预计 2 天)
  - [ ] 图片头像
  - [ ] 文字头像
  - [ ] 图标头像
  - [ ] 尺寸变体
  - [ ] 单元测试

- [ ] **Tag 标签** (预计 2 天)
  - [ ] 基础标签
  - [ ] 可关闭标签
  - [ ] 颜色变体
  - [ ] 尺寸变体
  - [ ] 单元测试

#### 2.2 导航组件
- [ ] **Menu 菜单** (预计 4-5 天)
  - [ ] 水平菜单
  - [ ] 垂直菜单
  - [ ] 多级菜单
  - [ ] 菜单折叠
  - [ ] 单元测试

- [ ] **Dropdown 下拉菜单** (预计 3-4 天)
  - [ ] 基础下拉菜单
  - [ ] 分组菜单
  - [ ] 多级菜单
  - [ ] 触发方式配置
  - [ ] 单元测试

#### 2.3 反馈组件
- [ ] **Tooltip 文字提示** (预计 3-4 天)
  - [ ] 基础提示
  - [ ] 多方向显示
  - [ ] 自定义内容
  - [ ] 触发方式配置
  - [ ] 单元测试

- [ ] **Popover 弹出框** (预计 3-4 天)
  - [ ] 基础弹出框
  - [ ] 自定义内容
  - [ ] 多方向显示
  - [ ] 触发方式配置
  - [ ] 单元测试

#### 2.4 数据录入
- [ ] **DatePicker 日期选择器** (预计 5-7 天)
  - [ ] 日期选择
  - [ ] 日期范围选择
  - [ ] 时间选择
  - [ ] 日期时间选择
  - [ ] 单元测试

- [ ] **Upload 上传** (预计 4-5 天)
  - [ ] 点击上传
  - [ ] 拖拽上传
  - [ ] 图片上传预览
  - [ ] 文件列表
  - [ ] 单元测试

### Phase 3: 增强组件 (优先级: P2)

#### 3.1 高级数据展示
- [ ] **Tree 树形控件** (预计 5-7 天)
- [ ] **Transfer 穿梭框** (预计 4-5 天)
- [ ] **Cascader 级联选择器** (预计 4-5 天)

#### 3.2 反馈增强
- [ ] **Progress 进度条** (预计 2-3 天)
- [ ] **Skeleton 骨架屏** (预计 2-3 天)
- [ ] **Empty 空状态** (预计 2 天)
- [ ] **Result 结果页** (预计 2-3 天)

#### 3.3 其他组件
- [ ] **InputNumber 数字输入框** (预计 2-3 天)
- [ ] **AutoComplete 自动完成** (预计 3-4 天)
- [ ] **ColorPicker 颜色选择器** (预计 4-5 天)
- [ ] **TimePicker 时间选择器** (预计 3-4 天)

### Phase 4: 高级特性 (优先级: P3)

#### 4.1 复杂组件
- [ ] **Calendar 日历** (预计 5-7 天)
- [ ] **Timeline 时间轴** (预计 3-4 天)
- [ ] **Anchor 锚点** (预计 2-3 天)
- [ ] **BackTop 回到顶部** (预计 1-2 天)

#### 4.2 系统增强
- [ ] **主题定制系统** (预计 3-4 天)
- [ ] **国际化支持** (预计 2-3 天)
- [ ] **无障碍访问优化** (预计 2-3 天)

## 📊 开发进度统计

### 总体进度
- **已完成**: 3/40 组件 (7.5%)
- **开发中**: 0/40 组件 (0%)
- **待开发**: 37/40 组件 (92.5%)

### 按优先级统计
- **P0 (核心)**: 0/8 组件完成
- **P1 (常用)**: 0/15 组件完成  
- **P2 (增强)**: 0/12 组件完成
- **P3 (高级)**: 0/5 组件完成

### 预计时间
- **Phase 1**: 2-3 周
- **Phase 2**: 3-4 周
- **Phase 3**: 4-5 周
- **Phase 4**: 2-3 周
- **总计**: 11-15 周

## 🛠️ 开发规范

### 组件开发流程
1. **设计确认**: 参考 component-library.html 设计规范
2. **类型定义**: 创建完整的 TypeScript 类型
3. **组件实现**: Vue 3 Composition API
4. **样式开发**: SCSS + CSS 变量
5. **单元测试**: Vitest + Vue Test Utils
6. **文档编写**: 组件 API 和使用示例
7. **代码审查**: ESLint + Prettier 检查

### 质量标准
- **TypeScript**: 100% 类型覆盖
- **测试覆盖率**: ≥ 80%
- **无障碍性**: 符合 WCAG 2.1 AA 标准
- **性能**: 组件渲染时间 < 16ms
- **包大小**: 单个组件 gzip < 10KB

## 🎯 里程碑

### Milestone 1: 核心基础 (Week 1-3)
- [ ] Checkbox, Radio, Form 组件
- [ ] Loading 组件
- [ ] 基础测试框架搭建

### Milestone 2: 常用功能 (Week 4-7)
- [ ] Card, Avatar, Tag 组件
- [ ] Menu, Dropdown 组件
- [ ] Tooltip, Popover 组件
- [ ] DatePicker, Upload 组件

### Milestone 3: 增强特性 (Week 8-12)
- [ ] Tree, Transfer, Cascader 组件
- [ ] Progress, Skeleton, Empty 组件
- [ ] 高级输入组件

### Milestone 4: 完善发布 (Week 13-15)
- [ ] Calendar, Timeline 组件
- [ ] 主题系统和国际化
- [ ] 文档完善和发布准备

## 📝 注意事项

1. **严格遵循设计系统**: 所有组件必须使用 component-library.html 中定义的设计标记
2. **保持 API 一致性**: 参考 Element Plus 和 Ant Design Vue 的 API 设计
3. **性能优先**: 使用 Vue 3 的最新特性优化性能
4. **测试驱动**: 每个组件都需要完整的单元测试
5. **文档同步**: 组件开发和文档编写同步进行
