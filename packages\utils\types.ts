export type Awaitable<T> = T | Promise<T>

export type Arrayable<T> = T | T[]

export type Nullable<T> = T | null

export type Recordable<T = any> = Record<string, T>

export type ComponentSize = 'small' | 'medium' | 'large'

export type ComponentType = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'

export type ComponentStatus = 'default' | 'hover' | 'active' | 'disabled' | 'loading'

export type Mutable<T> = {
  -readonly [P in keyof T]: T[P]
}

export type HTMLElementCustomized<T> = HTMLElement & T
