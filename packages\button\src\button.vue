<template>
  <button
    ref="buttonRef"
    :class="[
      'lz-button',
      `lz-button--${type}`,
      `lz-button--${size}`,
      {
        'lz-button--loading': loading,
        'lz-button--disabled': disabled,
        'lz-button--round': round,
        'lz-button--circle': circle,
        'lz-button--text': text,
        'lz-button--link': link,
      },
    ]"
    :disabled="disabled || loading"
    :type="nativeType"
    @click="handleClick"
  >
    <lz-icon v-if="loading" class="lz-button__loading-icon" name="mdi-loading" spin />
    <lz-icon v-else-if="icon" :name="icon" />
    <span v-if="$slots.default" class="lz-button__text">
      <slot />
    </span>
  </button>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { LzIcon } from '../../icon'
import { buttonProps, buttonEmits } from './button'
import type { ButtonInstance } from './instance'

defineOptions({
  name: 'LzButton',
})

const props = defineProps(buttonProps)
const emit = defineEmits(buttonEmits)

const buttonRef = ref<HTMLButtonElement>()

const handleClick = (evt: MouseEvent) => {
  if (props.disabled || props.loading) return
  emit('click', evt)
}

defineExpose<ButtonInstance>({
  ref: buttonRef,
  size: props.size,
  type: props.type,
  disabled: props.disabled,
  loading: props.loading,
})
</script>
