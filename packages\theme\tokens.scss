// LanZT UI Design Tokens
// 基于 component-library.html 中定义的设计系统

:root {
  /* 主色系 */
  --lz-primary: #0FD59D;
  --lz-primary-50: #ECFDF5;
  --lz-primary-100: #D1FAE5;
  --lz-primary-200: #A7F3D0;
  --lz-primary-300: #6EE7B7;
  --lz-primary-400: #34D399;
  --lz-primary-500: #0FD59D;
  --lz-primary-600: #059669;
  --lz-primary-700: #047857;
  --lz-primary-800: #065F46;
  --lz-primary-900: #064E3B;
  
  /* 辅助色系 */
  --lz-secondary: #4F46E5;
  --lz-secondary-50: #EEF2FF;
  --lz-secondary-100: #E0E7FF;
  --lz-secondary-200: #C7D2FE;
  --lz-secondary-300: #A5B4FC;
  --lz-secondary-400: #818CF8;
  --lz-secondary-500: #6366F1;
  --lz-secondary-600: #4F46E5;
  --lz-secondary-700: #4338CA;
  --lz-secondary-800: #3730A3;
  --lz-secondary-900: #312E81;
  
  /* 强调色系 */
  --lz-accent: #F59E0B;
  --lz-accent-50: #FFFBEB;
  --lz-accent-100: #FEF3C7;
  --lz-accent-200: #FDE68A;
  --lz-accent-300: #FCD34D;
  --lz-accent-400: #FBBF24;
  --lz-accent-500: #F59E0B;
  --lz-accent-600: #D97706;
  --lz-accent-700: #B45309;
  --lz-accent-800: #92400E;
  --lz-accent-900: #78350F;
  
  /* 状态色系 */
  --lz-success: #10B981;
  --lz-success-light: #D1FAE5;
  --lz-warning: #F59E0B;
  --lz-warning-light: #FEF3C7;
  --lz-error: #EF4444;
  --lz-error-light: #FEE2E2;
  --lz-info: #3B82F6;
  --lz-info-light: #DBEAFE;
  
  /* 中性色系 */
  --lz-gray-50: #F9FAFB;
  --lz-gray-100: #F3F4F6;
  --lz-gray-200: #E5E7EB;
  --lz-gray-300: #D1D5DB;
  --lz-gray-400: #9CA3AF;
  --lz-gray-500: #6B7280;
  --lz-gray-600: #4B5563;
  --lz-gray-700: #374151;
  --lz-gray-800: #1F2937;
  --lz-gray-900: #111827;
  
  /* 暗色主题变量 */
  --lz-dark-bg: #0F172A;
  --lz-dark-surface: #1E293B;
  --lz-dark-border: #334155;
  --lz-dark-text: #F1F5F9;
  --lz-dark-text-secondary: #94A3B8;
  
  /* 字体大小 */
  --lz-text-xs: 0.75rem;
  --lz-text-sm: 0.875rem;
  --lz-text-base: 1rem;
  --lz-text-lg: 1.125rem;
  --lz-text-xl: 1.25rem;
  --lz-text-2xl: 1.5rem;
  --lz-text-3xl: 1.875rem;
  --lz-text-4xl: 2.25rem;
  --lz-text-5xl: 3rem;
  
  /* 字体权重 */
  --lz-font-light: 300;
  --lz-font-normal: 400;
  --lz-font-medium: 500;
  --lz-font-semibold: 600;
  --lz-font-bold: 700;
  --lz-font-extrabold: 800;
  
  /* 行高 */
  --lz-leading-none: 1;
  --lz-leading-tight: 1.25;
  --lz-leading-snug: 1.375;
  --lz-leading-normal: 1.5;
  --lz-leading-relaxed: 1.625;
  --lz-leading-loose: 2;
  
  /* 间距 */
  --lz-space-0: 0;
  --lz-space-px: 1px;
  --lz-space-0-5: 0.125rem;
  --lz-space-1: 0.25rem;
  --lz-space-1-5: 0.375rem;
  --lz-space-2: 0.5rem;
  --lz-space-2-5: 0.625rem;
  --lz-space-3: 0.75rem;
  --lz-space-3-5: 0.875rem;
  --lz-space-4: 1rem;
  --lz-space-5: 1.25rem;
  --lz-space-6: 1.5rem;
  --lz-space-7: 1.75rem;
  --lz-space-8: 2rem;
  --lz-space-9: 2.25rem;
  --lz-space-10: 2.5rem;
  --lz-space-11: 2.75rem;
  --lz-space-12: 3rem;
  --lz-space-14: 3.5rem;
  --lz-space-16: 4rem;
  --lz-space-20: 5rem;
  --lz-space-24: 6rem;
  --lz-space-28: 7rem;
  --lz-space-32: 8rem;
  
  /* 圆角 */
  --lz-radius-none: 0;
  --lz-radius-sm: 0.125rem;
  --lz-radius: 0.25rem;
  --lz-radius-md: 0.375rem;
  --lz-radius-lg: 0.5rem;
  --lz-radius-xl: 0.75rem;
  --lz-radius-2xl: 1rem;
  --lz-radius-3xl: 1.5rem;
  --lz-radius-full: 9999px;
  
  /* 阴影 */
  --lz-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --lz-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --lz-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --lz-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --lz-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --lz-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --lz-shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  
  /* 渐变 */
  --lz-gradient-primary: linear-gradient(135deg, var(--lz-primary-400) 0%, var(--lz-primary-600) 100%);
  --lz-gradient-secondary: linear-gradient(135deg, var(--lz-secondary-400) 0%, var(--lz-secondary-600) 100%);
  --lz-gradient-warm: linear-gradient(135deg, var(--lz-accent-400) 0%, var(--lz-error) 100%);
  --lz-gradient-cool: linear-gradient(135deg, var(--lz-primary-400) 0%, var(--lz-secondary-500) 100%);
  
  /* 过渡动画 */
  --lz-transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --lz-transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --lz-transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --lz-transition-slower: 500ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Z-index层级 */
  --lz-z-dropdown: 1000;
  --lz-z-sticky: 1020;
  --lz-z-fixed: 1030;
  --lz-z-modal-backdrop: 1040;
  --lz-z-modal: 1050;
  --lz-z-popover: 1060;
  --lz-z-tooltip: 1070;
  --lz-z-toast: 1080;
}

/* 暗色主题 */
[data-theme="dark"] {
  --lz-gray-50: var(--lz-dark-text);
  --lz-gray-100: #E2E8F0;
  --lz-gray-200: #CBD5E1;
  --lz-gray-300: #94A3B8;
  --lz-gray-400: #64748B;
  --lz-gray-500: #475569;
  --lz-gray-600: #334155;
  --lz-gray-700: #1E293B;
  --lz-gray-800: #0F172A;
  --lz-gray-900: var(--lz-dark-bg);
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

/* 工具类 */
.lz-flex { display: flex; }
.lz-flex-col { flex-direction: column; }
.lz-flex-wrap { flex-wrap: wrap; }
.lz-items-center { align-items: center; }
.lz-items-start { align-items: flex-start; }
.lz-justify-center { justify-content: center; }
.lz-justify-between { justify-content: space-between; }
.lz-justify-start { justify-content: flex-start; }

.lz-grid { display: grid; }
.lz-grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.lz-grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.lz-grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.lz-grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

.lz-text-center { text-align: center; }
.lz-text-left { text-align: left; }
.lz-text-right { text-align: right; }

.lz-relative { position: relative; }
.lz-absolute { position: absolute; }
.lz-fixed { position: fixed; }

.lz-hidden { display: none; }
.lz-block { display: block; }
.lz-inline-block { display: inline-block; }

.lz-cursor-pointer { cursor: pointer; }
.lz-cursor-not-allowed { cursor: not-allowed; }

.lz-select-none { user-select: none; }

.lz-overflow-hidden { overflow: hidden; }
.lz-overflow-auto { overflow: auto; }

.lz-whitespace-nowrap { white-space: nowrap; }
.lz-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
