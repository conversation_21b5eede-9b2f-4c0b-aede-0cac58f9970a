<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网页视觉色彩方案与UI设计规范</title>
    <style>
        /* CSS变量定义 - 设计标记系统 */
        :root {
            /* 主色系 */
            --primary: #0FD59D;
            --primary-50: #ECFDF5;
            --primary-100: #D1FAE5;
            --primary-200: #A7F3D0;
            --primary-300: #6EE7B7;
            --primary-400: #34D399;
            --primary-500: #0FD59D;
            --primary-600: #059669;
            --primary-700: #047857;
            --primary-800: #065F46;
            --primary-900: #064E3B;

            /* 辅助色系 */
            --secondary: #4F46E5;
            --secondary-50: #EEF2FF;
            --secondary-100: #E0E7FF;
            --secondary-200: #C7D2FE;
            --secondary-300: #A5B4FC;
            --secondary-400: #818CF8;
            --secondary-500: #6366F1;
            --secondary-600: #4F46E5;
            --secondary-700: #4338CA;
            --secondary-800: #3730A3;
            --secondary-900: #312E81;

            /* 强调色系 */
            --accent: #F59E0B;
            --accent-50: #FFFBEB;
            --accent-100: #FEF3C7;
            --accent-200: #FDE68A;
            --accent-300: #FCD34D;
            --accent-400: #FBBF24;
            --accent-500: #F59E0B;
            --accent-600: #D97706;
            --accent-700: #B45309;
            --accent-800: #92400E;
            --accent-900: #78350F;

            /* 状态色系 */
            --success: #10B981;
            --success-light: #D1FAE5;
            --warning: #F59E0B;
            --warning-light: #FEF3C7;
            --error: #EF4444;
            --error-light: #FEE2E2;
            --info: #3B82F6;
            --info-light: #DBEAFE;

            /* 中性色系 */
            --gray-50: #F9FAFB;
            --gray-100: #F3F4F6;
            --gray-200: #E5E7EB;
            --gray-300: #D1D5DB;
            --gray-400: #9CA3AF;
            --gray-500: #6B7280;
            --gray-600: #4B5563;
            --gray-700: #374151;
            --gray-800: #1F2937;
            --gray-900: #111827;

            /* 字体大小 */
            --text-xs: 0.75rem;
            --text-sm: 0.875rem;
            --text-base: 1rem;
            --text-lg: 1.125rem;
            --text-xl: 1.25rem;
            --text-2xl: 1.5rem;
            --text-3xl: 1.875rem;
            --text-4xl: 2.25rem;
            --text-5xl: 3rem;

            /* 间距 */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;

            /* 圆角 */
            --radius-sm: 0.125rem;
            --radius: 0.25rem;
            --radius-md: 0.375rem;
            --radius-lg: 0.5rem;
            --radius-xl: 0.75rem;
            --radius-2xl: 1rem;
            --radius-full: 9999px;

            /* 阴影 */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

            /* 渐变 */
            --gradient-primary: linear-gradient(135deg, var(--primary-400) 0%, var(--primary-600) 100%);
            --gradient-secondary: linear-gradient(135deg, var(--secondary-400) 0%, var(--secondary-600) 100%);
            --gradient-warm: linear-gradient(135deg, var(--accent-400) 0%, var(--error) 100%);
            --gradient-cool: linear-gradient(135deg, var(--primary-400) 0%, var(--secondary-500) 100%);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--space-6);
            background-color: var(--gray-50);
        }

        h1, h2, h3, h4, h5, h6 {
            color: var(--gray-900);
            font-weight: 700;
            line-height: 1.2;
        }

        h1 { font-size: var(--text-4xl); margin: var(--space-8) 0 var(--space-6) 0; }
        h2 { font-size: var(--text-3xl); margin: var(--space-6) 0 var(--space-4) 0; }
        h3 { font-size: var(--text-2xl); margin: var(--space-5) 0 var(--space-3) 0; }

        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: var(--space-5);
            margin: var(--space-8) 0;
        }

        .color-box {
            height: 120px;
            border-radius: var(--radius-xl);
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            padding: var(--space-3);
            box-shadow: var(--shadow-md);
            color: white;
            font-size: var(--text-sm);
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            position: relative;
            overflow: hidden;
        }

        .color-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, transparent 0%, rgba(0,0,0,0.1) 100%);
        }

        .color-box-content {
            position: relative;
            z-index: 1;
        }

        /* 主色系 */
        .primary-50 { background-color: var(--primary-50); color: var(--gray-800); }
        .primary-100 { background-color: var(--primary-100); color: var(--gray-800); }
        .primary-200 { background-color: var(--primary-200); color: var(--gray-800); }
        .primary-300 { background-color: var(--primary-300); color: var(--gray-800); }
        .primary-400 { background-color: var(--primary-400); }
        .primary-500 { background-color: var(--primary-500); }
        .primary-600 { background-color: var(--primary-600); }
        .primary-700 { background-color: var(--primary-700); }
        .primary-800 { background-color: var(--primary-800); }
        .primary-900 { background-color: var(--primary-900); }

        /* 辅助色系 */
        .secondary-50 { background-color: var(--secondary-50); color: var(--gray-800); }
        .secondary-100 { background-color: var(--secondary-100); color: var(--gray-800); }
        .secondary-200 { background-color: var(--secondary-200); color: var(--gray-800); }
        .secondary-300 { background-color: var(--secondary-300); color: var(--gray-800); }
        .secondary-400 { background-color: var(--secondary-400); }
        .secondary-500 { background-color: var(--secondary-500); }
        .secondary-600 { background-color: var(--secondary-600); }
        .secondary-700 { background-color: var(--secondary-700); }
        .secondary-800 { background-color: var(--secondary-800); }
        .secondary-900 { background-color: var(--secondary-900); }

        /* 强调色系 */
        .accent-50 { background-color: var(--accent-50); color: var(--gray-800); }
        .accent-100 { background-color: var(--accent-100); color: var(--gray-800); }
        .accent-200 { background-color: var(--accent-200); color: var(--gray-800); }
        .accent-300 { background-color: var(--accent-300); color: var(--gray-800); }
        .accent-400 { background-color: var(--accent-400); }
        .accent-500 { background-color: var(--accent-500); }
        .accent-600 { background-color: var(--accent-600); }
        .accent-700 { background-color: var(--accent-700); }
        .accent-800 { background-color: var(--accent-800); }
        .accent-900 { background-color: var(--accent-900); }

        /* 状态色系 */
        .success { background-color: var(--success); }
        .success-light { background-color: var(--success-light); color: var(--gray-800); }
        .warning { background-color: var(--warning); }
        .warning-light { background-color: var(--warning-light); color: var(--gray-800); }
        .error { background-color: var(--error); }
        .error-light { background-color: var(--error-light); color: var(--gray-800); }
        .info { background-color: var(--info); }
        .info-light { background-color: var(--info-light); color: var(--gray-800); }

        /* 中性色系 */
        .gray-50 { background-color: var(--gray-50); color: var(--gray-800); }
        .gray-100 { background-color: var(--gray-100); color: var(--gray-800); }
        .gray-200 { background-color: var(--gray-200); color: var(--gray-800); }
        .gray-300 { background-color: var(--gray-300); color: var(--gray-800); }
        .gray-400 { background-color: var(--gray-400); color: white; }
        .gray-500 { background-color: var(--gray-500); color: white; }
        .gray-600 { background-color: var(--gray-600); color: white; }
        .gray-700 { background-color: var(--gray-700); color: white; }
        .gray-800 { background-color: var(--gray-800); color: white; }
        .gray-900 { background-color: var(--gray-900); color: white; }

        /* 渐变色展示 */
        .gradient-primary { background: var(--gradient-primary); }
        .gradient-secondary { background: var(--gradient-secondary); }
        .gradient-warm { background: var(--gradient-warm); }
        .gradient-cool { background: var(--gradient-cool); }

        .section {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--space-8);
            margin: var(--space-8) 0;
            box-shadow: var(--shadow);
        }

        .ui-examples {
            margin: var(--space-8) 0;
        }

        /* 按钮样式 */
        .button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-3) var(--space-6);
            border-radius: var(--radius-lg);
            font-weight: 600;
            font-size: var(--text-sm);
            text-decoration: none;
            margin: var(--space-2);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .button:hover::before {
            left: 100%;
        }

        .button-sm {
            padding: var(--space-2) var(--space-4);
            font-size: var(--text-xs);
        }

        .button-lg {
            padding: var(--space-4) var(--space-8);
            font-size: var(--text-lg);
        }

        .button-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .button-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .button-secondary {
            background: var(--gradient-secondary);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .button-secondary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .button-accent {
            background: var(--gradient-warm);
            color: white;
            box-shadow: var(--shadow-md);
        }

        .button-accent:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .button-outline {
            background-color: transparent;
            border: 2px solid var(--primary-500);
            color: var(--primary-600);
        }

        .button-outline:hover {
            background-color: var(--primary-50);
            border-color: var(--primary-600);
        }

        .button-ghost {
            background-color: transparent;
            color: var(--gray-600);
        }

        .button-ghost:hover {
            background-color: var(--gray-100);
            color: var(--gray-800);
        }

        .button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: var(--radius-xl);
            overflow: hidden;
            box-shadow: var(--shadow-md);
            margin: var(--space-5);
            transition: all 0.3s ease;
            border: 1px solid var(--gray-200);
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .card-header {
            height: 160px;
            background: var(--gradient-primary);
            position: relative;
            overflow: hidden;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        }

        .card-body {
            padding: var(--space-6);
        }

        .card-title {
            font-size: var(--text-xl);
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: var(--space-3);
        }

        .card-text {
            color: var(--gray-600);
            line-height: 1.6;
            margin-bottom: var(--space-4);
        }

        .card-footer {
            padding: var(--space-4) var(--space-6);
            background-color: var(--gray-50);
            border-top: 1px solid var(--gray-200);
        }

        /* 排版样式 */
        .typography-example h1 {
            font-size: var(--text-5xl);
            font-weight: 800;
            margin: var(--space-6) 0 var(--space-4) 0;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .typography-example h2 {
            font-size: var(--text-4xl);
            font-weight: 700;
            margin: var(--space-5) 0 var(--space-3) 0;
        }

        .typography-example h3 {
            font-size: var(--text-3xl);
            font-weight: 600;
            margin: var(--space-4) 0 var(--space-3) 0;
        }

        .typography-example h4 {
            font-size: var(--text-2xl);
            font-weight: 600;
            margin: var(--space-3) 0 var(--space-2) 0;
        }

        .typography-example p {
            margin: var(--space-3) 0;
            font-size: var(--text-base);
            line-height: 1.7;
            color: var(--gray-700);
        }

        .typography-example .lead {
            font-size: var(--text-xl);
            font-weight: 400;
            color: var(--gray-600);
            line-height: 1.6;
        }

        .typography-example .small {
            font-size: var(--text-sm);
            color: var(--gray-500);
        }

        .typography-example a {
            color: var(--primary-600);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s ease;
        }

        .typography-example a:hover {
            color: var(--primary-700);
            text-decoration: underline;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: var(--space-6);
        }

        .form-label {
            display: block;
            margin-bottom: var(--space-2);
            font-weight: 600;
            font-size: var(--text-sm);
            color: var(--gray-700);
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            max-width: 400px;
            padding: var(--space-3) var(--space-4);
            border: 2px solid var(--gray-300);
            border-radius: var(--radius-lg);
            font-size: var(--text-base);
            transition: all 0.2s ease;
            background-color: white;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-500);
            box-shadow: 0 0 0 3px rgba(15, 213, 157, 0.1);
            transform: translateY(-1px);
        }

        .form-input:invalid {
            border-color: var(--error);
        }

        .form-help {
            margin-top: var(--space-1);
            font-size: var(--text-xs);
            color: var(--gray-500);
        }

        .form-error {
            margin-top: var(--space-1);
            font-size: var(--text-xs);
            color: var(--error);
        }

        /* 复选框和单选框 */
        .checkbox, .radio {
            display: flex;
            align-items: center;
            margin: var(--space-2) 0;
        }

        .checkbox input, .radio input {
            width: auto;
            margin-right: var(--space-2);
            transform: scale(1.2);
            accent-color: var(--primary-500);
        }

        /* 提示信息样式 */
        .alert {
            padding: var(--space-4) var(--space-5);
            border-radius: var(--radius-lg);
            margin: var(--space-4) 0;
            border-left: 4px solid;
            position: relative;
            overflow: hidden;
        }

        .alert::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, transparent 0%, rgba(255,255,255,0.1) 100%);
            pointer-events: none;
        }

        .alert-success {
            background-color: var(--success-light);
            color: var(--primary-800);
            border-left-color: var(--success);
        }

        .alert-info {
            background-color: var(--info-light);
            color: var(--secondary-800);
            border-left-color: var(--info);
        }

        .alert-warning {
            background-color: var(--warning-light);
            color: var(--accent-800);
            border-left-color: var(--warning);
        }

        .alert-error {
            background-color: var(--error-light);
            color: var(--error);
            border-left-color: var(--error);
        }

        .alert-title {
            font-weight: 600;
            margin-bottom: var(--space-1);
        }

        /* 徽章样式 */
        .badge {
            display: inline-flex;
            align-items: center;
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-full);
            font-size: var(--text-xs);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .badge-primary {
            background-color: var(--primary-100);
            color: var(--primary-800);
        }

        .badge-secondary {
            background-color: var(--secondary-100);
            color: var(--secondary-800);
        }

        .badge-success {
            background-color: var(--success-light);
            color: var(--primary-800);
        }

        .badge-warning {
            background-color: var(--warning-light);
            color: var(--accent-800);
        }

        .badge-error {
            background-color: var(--error-light);
            color: var(--error);
        }

        /* 间距展示 */
        .spacing-scale {
            margin: var(--space-8) 0;
        }

        .spacing-item {
            margin: var(--space-3) 0;
            display: flex;
            align-items: center;
            padding: var(--space-2);
            border-radius: var(--radius);
            transition: background-color 0.2s ease;
        }

        .spacing-item:hover {
            background-color: var(--gray-100);
        }

        .spacing-box {
            display: inline-block;
            background: var(--gradient-primary);
            margin-right: var(--space-4);
            border-radius: var(--radius-sm);
            box-shadow: var(--shadow-sm);
        }

        .spacing-1 { width: 4px; height: 4px; }
        .spacing-2 { width: 8px; height: 8px; }
        .spacing-3 { width: 12px; height: 12px; }
        .spacing-4 { width: 16px; height: 16px; }
        .spacing-5 { width: 20px; height: 20px; }
        .spacing-6 { width: 24px; height: 24px; }
        .spacing-8 { width: 32px; height: 32px; }
        .spacing-10 { width: 40px; height: 40px; }
        .spacing-12 { width: 48px; height: 48px; }
        .spacing-16 { width: 64px; height: 64px; }
        .spacing-20 { width: 80px; height: 80px; }

        /* 圆角展示 */
        .radius-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--space-4);
            margin: var(--space-6) 0;
        }

        .radius-item {
            width: 80px;
            height: 80px;
            background: var(--gradient-cool);
            margin: var(--space-2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: var(--text-xs);
            text-align: center;
        }

        .radius-sm { border-radius: var(--radius-sm); }
        .radius { border-radius: var(--radius); }
        .radius-md { border-radius: var(--radius-md); }
        .radius-lg { border-radius: var(--radius-lg); }
        .radius-xl { border-radius: var(--radius-xl); }
        .radius-2xl { border-radius: var(--radius-2xl); }
        .radius-full { border-radius: var(--radius-full); }

        /* 阴影展示 */
        .shadow-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--space-6);
            margin: var(--space-6) 0;
        }

        .shadow-item {
            width: 120px;
            height: 80px;
            background: white;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: var(--text-sm);
            color: var(--gray-700);
            margin: var(--space-4);
        }

        .shadow-sm { box-shadow: var(--shadow-sm); }
        .shadow { box-shadow: var(--shadow); }
        .shadow-md { box-shadow: var(--shadow-md); }
        .shadow-lg { box-shadow: var(--shadow-lg); }
        .shadow-xl { box-shadow: var(--shadow-xl); }

        /* 工具类 */
        .text-center { text-align: center; }
        .text-left { text-align: left; }
        .text-right { text-align: right; }

        .flex { display: flex; }
        .flex-col { flex-direction: column; }
        .items-center { align-items: center; }
        .justify-center { justify-content: center; }
        .justify-between { justify-content: space-between; }

        .grid { display: grid; }
        .grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
        .grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
        .grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
        .gap-4 { gap: var(--space-4); }

        .mb-4 { margin-bottom: var(--space-4); }
        .mt-4 { margin-top: var(--space-4); }
        .mr-4 { margin-right: var(--space-4); }
        .ml-4 { margin-left: var(--space-4); }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: var(--space-4);
            }

            .color-palette {
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            }

            .card {
                margin: var(--space-3) 0;
            }

            .button {
                width: 100%;
                margin: var(--space-1) 0;
            }

            .form-input, .form-select, .form-textarea {
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <header class="text-center mb-4">
        <h1>网页视觉色彩方案与UI设计规范</h1>
        <p class="lead">基于主色调 #0FD59D 的现代化设计系统</p>
        <div class="flex justify-center mt-4">
            <span class="badge badge-primary">v1.0</span>
            <span class="badge badge-success ml-4">完整版</span>
        </div>
    </header>

    <div class="section">
        <h2>🎨 1. 主色系 (Primary Colors)</h2>
        <p>基于 #0FD59D 青绿色构建的完整色彩层级，提供从浅到深的10个层次。</p>
        <div class="color-palette">
            <div class="color-box primary-50">
                <div class="color-box-content">
                    <strong>50</strong><br>
                    #ECFDF5
                </div>
            </div>
            <div class="color-box primary-100">
                <div class="color-box-content">
                    <strong>100</strong><br>
                    #D1FAE5
                </div>
            </div>
            <div class="color-box primary-200">
                <div class="color-box-content">
                    <strong>200</strong><br>
                    #A7F3D0
                </div>
            </div>
            <div class="color-box primary-300">
                <div class="color-box-content">
                    <strong>300</strong><br>
                    #6EE7B7
                </div>
            </div>
            <div class="color-box primary-400">
                <div class="color-box-content">
                    <strong>400</strong><br>
                    #34D399
                </div>
            </div>
            <div class="color-box primary-500">
                <div class="color-box-content">
                    <strong>500 主色</strong><br>
                    #0FD59D
                </div>
            </div>
            <div class="color-box primary-600">
                <div class="color-box-content">
                    <strong>600</strong><br>
                    #059669
                </div>
            </div>
            <div class="color-box primary-700">
                <div class="color-box-content">
                    <strong>700</strong><br>
                    #047857
                </div>
            </div>
            <div class="color-box primary-800">
                <div class="color-box-content">
                    <strong>800</strong><br>
                    #065F46
                </div>
            </div>
            <div class="color-box primary-900">
                <div class="color-box-content">
                    <strong>900</strong><br>
                    #064E3B
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>🔵 2. 辅助色系 (Secondary Colors)</h2>
        <p>蓝紫色系，用于次要操作和信息展示。</p>
        <div class="color-palette">
            <div class="color-box secondary-100">
                <div class="color-box-content">
                    <strong>100</strong><br>
                    #E0E7FF
                </div>
            </div>
            <div class="color-box secondary-200">
                <div class="color-box-content">
                    <strong>200</strong><br>
                    #C7D2FE
                </div>
            </div>
            <div class="color-box secondary-300">
                <div class="color-box-content">
                    <strong>300</strong><br>
                    #A5B4FC
                </div>
            </div>
            <div class="color-box secondary-400">
                <div class="color-box-content">
                    <strong>400</strong><br>
                    #818CF8
                </div>
            </div>
            <div class="color-box secondary-500">
                <div class="color-box-content">
                    <strong>500</strong><br>
                    #6366F1
                </div>
            </div>
            <div class="color-box secondary-600">
                <div class="color-box-content">
                    <strong>600 主辅助色</strong><br>
                    #4F46E5
                </div>
            </div>
            <div class="color-box secondary-700">
                <div class="color-box-content">
                    <strong>700</strong><br>
                    #4338CA
                </div>
            </div>
            <div class="color-box secondary-800">
                <div class="color-box-content">
                    <strong>800</strong><br>
                    #3730A3
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>🟡 3. 强调色系 (Accent Colors)</h2>
        <p>橙黄色系，用于警告和特殊强调。</p>
        <div class="color-palette">
            <div class="color-box accent-100">
                <div class="color-box-content">
                    <strong>100</strong><br>
                    #FEF3C7
                </div>
            </div>
            <div class="color-box accent-200">
                <div class="color-box-content">
                    <strong>200</strong><br>
                    #FDE68A
                </div>
            </div>
            <div class="color-box accent-300">
                <div class="color-box-content">
                    <strong>300</strong><br>
                    #FCD34D
                </div>
            </div>
            <div class="color-box accent-400">
                <div class="color-box-content">
                    <strong>400</strong><br>
                    #FBBF24
                </div>
            </div>
            <div class="color-box accent-500">
                <div class="color-box-content">
                    <strong>500 主强调色</strong><br>
                    #F59E0B
                </div>
            </div>
            <div class="color-box accent-600">
                <div class="color-box-content">
                    <strong>600</strong><br>
                    #D97706
                </div>
            </div>
            <div class="color-box accent-700">
                <div class="color-box-content">
                    <strong>700</strong><br>
                    #B45309
                </div>
            </div>
            <div class="color-box accent-800">
                <div class="color-box-content">
                    <strong>800</strong><br>
                    #92400E
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>🚦 4. 状态色系 (Status Colors)</h2>
        <p>用于表示不同状态和反馈信息的专用色彩。</p>
        <div class="color-palette">
            <div class="color-box success">
                <div class="color-box-content">
                    <strong>成功</strong><br>
                    #10B981
                </div>
            </div>
            <div class="color-box success-light">
                <div class="color-box-content">
                    <strong>成功浅色</strong><br>
                    #D1FAE5
                </div>
            </div>
            <div class="color-box info">
                <div class="color-box-content">
                    <strong>信息</strong><br>
                    #3B82F6
                </div>
            </div>
            <div class="color-box info-light">
                <div class="color-box-content">
                    <strong>信息浅色</strong><br>
                    #DBEAFE
                </div>
            </div>
            <div class="color-box warning">
                <div class="color-box-content">
                    <strong>警告</strong><br>
                    #F59E0B
                </div>
            </div>
            <div class="color-box warning-light">
                <div class="color-box-content">
                    <strong>警告浅色</strong><br>
                    #FEF3C7
                </div>
            </div>
            <div class="color-box error">
                <div class="color-box-content">
                    <strong>错误</strong><br>
                    #EF4444
                </div>
            </div>
            <div class="color-box error-light">
                <div class="color-box-content">
                    <strong>错误浅色</strong><br>
                    #FEE2E2
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>⚫ 5. 中性色系 (Neutral Colors)</h2>
        <p>用于文本、边框、背景等基础元素的灰色系列。</p>
        <div class="color-palette">
            <div class="color-box gray-50">
                <div class="color-box-content">
                    <strong>50</strong><br>
                    #F9FAFB
                </div>
            </div>
            <div class="color-box gray-100">
                <div class="color-box-content">
                    <strong>100</strong><br>
                    #F3F4F6
                </div>
            </div>
            <div class="color-box gray-200">
                <div class="color-box-content">
                    <strong>200</strong><br>
                    #E5E7EB
                </div>
            </div>
            <div class="color-box gray-300">
                <div class="color-box-content">
                    <strong>300</strong><br>
                    #D1D5DB
                </div>
            </div>
            <div class="color-box gray-400">
                <div class="color-box-content">
                    <strong>400</strong><br>
                    #9CA3AF
                </div>
            </div>
            <div class="color-box gray-500">
                <div class="color-box-content">
                    <strong>500</strong><br>
                    #6B7280
                </div>
            </div>
            <div class="color-box gray-600">
                <div class="color-box-content">
                    <strong>600</strong><br>
                    #4B5563
                </div>
            </div>
            <div class="color-box gray-700">
                <div class="color-box-content">
                    <strong>700</strong><br>
                    #374151
                </div>
            </div>
            <div class="color-box gray-800">
                <div class="color-box-content">
                    <strong>800</strong><br>
                    #1F2937
                </div>
            </div>
            <div class="color-box gray-900">
                <div class="color-box-content">
                    <strong>900</strong><br>
                    #111827
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>🌈 6. 渐变色方案 (Gradients)</h2>
        <p>现代化的渐变效果，用于背景、按钮和装饰元素。</p>
        <div class="color-palette">
            <div class="color-box gradient-primary">
                <div class="color-box-content">
                    <strong>主渐变</strong><br>
                    Primary Gradient
                </div>
            </div>
            <div class="color-box gradient-secondary">
                <div class="color-box-content">
                    <strong>辅助渐变</strong><br>
                    Secondary Gradient
                </div>
            </div>
            <div class="color-box gradient-warm">
                <div class="color-box-content">
                    <strong>暖色渐变</strong><br>
                    Warm Gradient
                </div>
            </div>
            <div class="color-box gradient-cool">
                <div class="color-box-content">
                    <strong>冷色渐变</strong><br>
                    Cool Gradient
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <h3>📋 色彩使用指南</h3>
        <div class="grid grid-cols-2 gap-4">
            <div>
                <h4>主色系使用场景</h4>
                <ul>
                    <li><strong>Primary 500:</strong> 主要按钮、重要链接、品牌元素</li>
                    <li><strong>Primary 600-900:</strong> 标题、重要文本、悬停状态</li>
                    <li><strong>Primary 50-400:</strong> 背景高亮、次要元素、禁用状态</li>
                </ul>
            </div>
            <div>
                <h4>辅助色系使用场景</h4>
                <ul>
                    <li><strong>Secondary 600:</strong> 次要按钮、信息提示</li>
                    <li><strong>Accent 500:</strong> 警告、特殊强调</li>
                    <li><strong>Status Colors:</strong> 成功、错误、警告、信息反馈</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>🔘 7. 按钮系统 (Button System)</h2>
        <p>完整的按钮样式系统，包含不同尺寸、状态和变体。</p>

        <h3>按钮类型</h3>
        <div class="mb-4">
            <button class="button button-primary">主要按钮</button>
            <button class="button button-secondary">次要按钮</button>
            <button class="button button-accent">强调按钮</button>
            <button class="button button-outline">轮廓按钮</button>
            <button class="button button-ghost">幽灵按钮</button>
        </div>

        <h3>按钮尺寸</h3>
        <div class="mb-4">
            <button class="button button-primary button-sm">小按钮</button>
            <button class="button button-primary">标准按钮</button>
            <button class="button button-primary button-lg">大按钮</button>
        </div>

        <h3>按钮状态</h3>
        <div class="mb-4">
            <button class="button button-primary">正常状态</button>
            <button class="button button-primary" disabled>禁用状态</button>
        </div>
    </div>

    <div class="section">
        <h2>🃏 8. 卡片设计 (Card Design)</h2>
        <p>现代化的卡片组件，支持多种布局和内容类型。</p>
        <div class="flex justify-center">
            <div class="card">
                <div class="card-header"></div>
                <div class="card-body">
                    <h3 class="card-title">产品卡片</h3>
                    <p class="card-text">这是一个现代化的卡片设计示例，具有渐变背景、优雅的阴影效果和流畅的悬停动画。</p>
                    <div class="flex justify-between items-center">
                        <span class="badge badge-primary">新品</span>
                        <button class="button button-primary button-sm">查看详情</button>
                    </div>
                </div>
                <div class="card-footer">
                    <small class="text-gray-500">2024年8月15日</small>
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>📝 9. 排版系统 (Typography System)</h2>
        <p>完整的文字层级系统，确保内容的可读性和视觉层次。</p>
        <div class="typography-example">
            <h1>主标题 H1 (3rem/48px)</h1>
            <h2>二级标题 H2 (2.25rem/36px)</h2>
            <h3>三级标题 H3 (1.875rem/30px)</h3>
            <h4>四级标题 H4 (1.5rem/24px)</h4>

            <p class="lead">引导段落 - 用于重要的开篇文字，字体稍大，颜色稍浅。</p>

            <p>正文段落 (1rem/16px) - 这是标准的正文文本样式。行高设置为1.7以提供良好的可读性。字体颜色使用中性灰色确保足够的对比度和舒适的阅读体验。</p>

            <p class="small">小字文本 - 用于次要信息、版权声明、注释等内容。</p>

            <p>文本中可以包含 <a href="#">链接文本</a>，链接使用主色调并在悬停时显示下划线效果。</p>

            <blockquote style="border-left: 4px solid var(--primary-500); padding-left: var(--space-4); margin: var(--space-6) 0; font-style: italic; color: var(--gray-600);">
                "优秀的设计不仅仅是外观和感觉，更重要的是它如何工作。" - Steve Jobs
            </blockquote>
        </div>
    </div>

    <div class="section">
        <h2>📋 10. 表单系统 (Form System)</h2>
        <p>现代化的表单组件，提供良好的用户体验和视觉反馈。</p>

        <div class="grid grid-cols-2 gap-4">
            <div>
                <div class="form-group">
                    <label class="form-label" for="name">姓名</label>
                    <input type="text" id="name" class="form-input" placeholder="请输入您的姓名">
                    <div class="form-help">请输入您的真实姓名</div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="email">邮箱地址</label>
                    <input type="email" id="email" class="form-input" placeholder="<EMAIL>">
                </div>

                <div class="form-group">
                    <label class="form-label" for="password">密码</label>
                    <input type="password" id="password" class="form-input" placeholder="请输入密码">
                    <div class="form-error">密码长度至少8位</div>
                </div>
            </div>

            <div>
                <div class="form-group">
                    <label class="form-label" for="select">选择选项</label>
                    <select id="select" class="form-select">
                        <option>请选择...</option>
                        <option>选项1</option>
                        <option>选项2</option>
                        <option>选项3</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label" for="textarea">反馈内容</label>
                    <textarea id="textarea" class="form-textarea" rows="4" placeholder="请输入您的反馈"></textarea>
                </div>

                <div class="form-group">
                    <div class="checkbox">
                        <input type="checkbox" id="agree">
                        <label for="agree">我同意服务条款</label>
                    </div>

                    <div class="radio">
                        <input type="radio" id="option1" name="options">
                        <label for="option1">选项1</label>
                    </div>

                    <div class="radio">
                        <input type="radio" id="option2" name="options">
                        <label for="option2">选项2</label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>💬 11. 提示信息 (Alert System)</h2>
        <p>用于向用户传达重要信息的提示组件系统。</p>

        <div class="alert alert-success">
            <div class="alert-title">操作成功</div>
            您的数据已成功保存，系统将在3秒后自动跳转。
        </div>

        <div class="alert alert-info">
            <div class="alert-title">温馨提示</div>
            为了更好的用户体验，建议您使用最新版本的浏览器。
        </div>

        <div class="alert alert-warning">
            <div class="alert-title">注意事项</div>
            您的会话即将过期，请及时保存当前工作内容。
        </div>

        <div class="alert alert-error">
            <div class="alert-title">错误信息</div>
            网络连接失败，请检查您的网络设置后重试。
        </div>
    </div>

    <div class="section">
        <h2>🏷️ 12. 徽章系统 (Badge System)</h2>
        <p>用于标记状态、分类或计数的小型标签组件。</p>

        <div class="mb-4">
            <span class="badge badge-primary">主要</span>
            <span class="badge badge-secondary">次要</span>
            <span class="badge badge-success">成功</span>
            <span class="badge badge-warning">警告</span>
            <span class="badge badge-error">错误</span>
        </div>

        <p>徽章可以与其他元素组合使用：</p>
        <div class="flex items-center">
            <h3 style="margin: 0;">产品列表</h3>
            <span class="badge badge-primary ml-4">12</span>
        </div>
    </div>

    <div class="section">
        <h2>📏 13. 间距系统 (Spacing System)</h2>
        <p>基于4px基准的间距系统，确保设计的一致性和节奏感。</p>
        <div class="spacing-scale">
            <div class="spacing-item">
                <span class="spacing-box spacing-1"></span>
                <span><strong>4px (0.25rem)</strong> - 微小间距，用于紧密相关的元素</span>
            </div>
            <div class="spacing-item">
                <span class="spacing-box spacing-2"></span>
                <span><strong>8px (0.5rem)</strong> - 小间距，用于元素内部间距</span>
            </div>
            <div class="spacing-item">
                <span class="spacing-box spacing-3"></span>
                <span><strong>12px (0.75rem)</strong> - 标准小间距</span>
            </div>
            <div class="spacing-item">
                <span class="spacing-box spacing-4"></span>
                <span><strong>16px (1rem)</strong> - 基础间距，最常用的间距</span>
            </div>
            <div class="spacing-item">
                <span class="spacing-box spacing-5"></span>
                <span><strong>20px (1.25rem)</strong> - 中等间距</span>
            </div>
            <div class="spacing-item">
                <span class="spacing-box spacing-6"></span>
                <span><strong>24px (1.5rem)</strong> - 标准间距</span>
            </div>
            <div class="spacing-item">
                <span class="spacing-box spacing-8"></span>
                <span><strong>32px (2rem)</strong> - 大间距，用于组件间距</span>
            </div>
            <div class="spacing-item">
                <span class="spacing-box spacing-10"></span>
                <span><strong>40px (2.5rem)</strong> - 超大间距</span>
            </div>
            <div class="spacing-item">
                <span class="spacing-box spacing-12"></span>
                <span><strong>48px (3rem)</strong> - 区块间距</span>
            </div>
            <div class="spacing-item">
                <span class="spacing-box spacing-16"></span>
                <span><strong>64px (4rem)</strong> - 超大区块间距</span>
            </div>
            <div class="spacing-item">
                <span class="spacing-box spacing-20"></span>
                <span><strong>80px (5rem)</strong> - 页面级间距</span>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>🔲 14. 圆角系统 (Border Radius)</h2>
        <p>统一的圆角规范，创造现代化的视觉效果。</p>
        <div class="radius-demo">
            <div class="radius-item radius-sm">2px<br>小圆角</div>
            <div class="radius-item radius">4px<br>标准</div>
            <div class="radius-item radius-md">6px<br>中等</div>
            <div class="radius-item radius-lg">8px<br>大圆角</div>
            <div class="radius-item radius-xl">12px<br>超大</div>
            <div class="radius-item radius-2xl">16px<br>特大</div>
            <div class="radius-item radius-full">圆形<br>全圆角</div>
        </div>

        <h3>使用指南</h3>
        <ul>
            <li><strong>2-4px:</strong> 按钮、输入框、徽章等小元素</li>
            <li><strong>6-8px:</strong> 卡片、弹出框等中等大小元素</li>
            <li><strong>12-16px:</strong> 大尺寸容器、特殊强调组件</li>
            <li><strong>全圆角:</strong> 头像、图标、特殊装饰元素</li>
        </ul>
    </div>

    <div class="section">
        <h2>🌫️ 15. 阴影系统 (Shadow System)</h2>
        <p>层次化的阴影系统，营造深度感和空间层次。</p>
        <div class="shadow-demo">
            <div class="shadow-item shadow-sm">小阴影<br>悬浮按钮</div>
            <div class="shadow-item shadow">标准阴影<br>卡片元素</div>
            <div class="shadow-item shadow-md">中等阴影<br>下拉菜单</div>
            <div class="shadow-item shadow-lg">大阴影<br>模态框</div>
            <div class="shadow-item shadow-xl">超大阴影<br>弹出层</div>
        </div>

        <h3>阴影层级</h3>
        <ul>
            <li><strong>Small:</strong> 0 1px 2px rgba(0,0,0,0.05) - 微妙的悬浮效果</li>
            <li><strong>Default:</strong> 0 1px 3px rgba(0,0,0,0.1) - 标准卡片阴影</li>
            <li><strong>Medium:</strong> 0 4px 6px rgba(0,0,0,0.1) - 下拉菜单、工具提示</li>
            <li><strong>Large:</strong> 0 10px 15px rgba(0,0,0,0.1) - 模态框、侧边栏</li>
            <li><strong>Extra Large:</strong> 0 20px 25px rgba(0,0,0,0.1) - 大型弹出层</li>
        </ul>
    </div>

    <div class="section">
        <h2>🎯 16. 设计原则 (Design Principles)</h2>
        <div class="grid grid-cols-2 gap-4">
            <div>
                <h3>核心原则</h3>
                <ol>
                    <li><strong>一致性 (Consistency):</strong> 所有UI元素保持统一的视觉语言和交互模式</li>
                    <li><strong>层次感 (Hierarchy):</strong> 通过色彩、字体大小和间距建立清晰的信息层级</li>
                    <li><strong>可访问性 (Accessibility):</strong> 确保色彩对比度符合WCAG标准，支持无障碍访问</li>
                    <li><strong>响应式 (Responsive):</strong> 适配各种屏幕尺寸和设备类型</li>
                </ol>
            </div>
            <div>
                <h3>品牌特性</h3>
                <ol>
                    <li><strong>现代感 (Modern):</strong> 采用当代设计趋势，保持时尚感</li>
                    <li><strong>清新感 (Fresh):</strong> 利用青绿色主调营造清新自然的氛围</li>
                    <li><strong>专业性 (Professional):</strong> 平衡美观与功能，体现专业品质</li>
                    <li><strong>友好性 (Friendly):</strong> 温和的色彩搭配，创造亲和的用户体验</li>
                </ol>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>💻 17. CSS变量定义 (CSS Variables)</h2>
        <p>完整的CSS变量系统，便于开发和维护。</p>

        <div style="background-color: var(--gray-900); color: var(--gray-100); padding: var(--space-6); border-radius: var(--radius-lg); font-family: 'Courier New', monospace; font-size: var(--text-sm); line-height: 1.6; overflow-x: auto;">
<pre style="margin: 0; white-space: pre-wrap;">/* 主色系变量 */
--primary: #0FD59D;
--primary-50: #ECFDF5;
--primary-100: #D1FAE5;
--primary-200: #A7F3D0;
--primary-300: #6EE7B7;
--primary-400: #34D399;
--primary-500: #0FD59D;
--primary-600: #059669;
--primary-700: #047857;
--primary-800: #065F46;
--primary-900: #064E3B;

/* 辅助色系变量 */
--secondary: #4F46E5;
--secondary-100: #E0E7FF;
--secondary-600: #4F46E5;

/* 状态色变量 */
--success: #10B981;
--warning: #F59E0B;
--error: #EF4444;
--info: #3B82F6;

/* 中性色变量 */
--gray-50: #F9FAFB;
--gray-100: #F3F4F6;
--gray-500: #6B7280;
--gray-900: #111827;

/* 字体大小变量 */
--text-xs: 0.75rem;
--text-sm: 0.875rem;
--text-base: 1rem;
--text-lg: 1.125rem;
--text-xl: 1.25rem;
--text-2xl: 1.5rem;
--text-3xl: 1.875rem;
--text-4xl: 2.25rem;
--text-5xl: 3rem;

/* 间距变量 */
--space-1: 0.25rem;
--space-2: 0.5rem;
--space-3: 0.75rem;
--space-4: 1rem;
--space-6: 1.5rem;
--space-8: 2rem;

/* 圆角变量 */
--radius-sm: 0.125rem;
--radius: 0.25rem;
--radius-lg: 0.5rem;
--radius-xl: 0.75rem;
--radius-full: 9999px;

/* 阴影变量 */
--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
--shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

/* 渐变变量 */
--gradient-primary: linear-gradient(135deg, var(--primary-400) 0%, var(--primary-600) 100%);
--gradient-secondary: linear-gradient(135deg, var(--secondary-400) 0%, var(--secondary-600) 100%);</pre>
        </div>
    </div>

    <div class="section">
        <h2>🚀 18. 实施指南 (Implementation Guide)</h2>
        <div class="grid grid-cols-2 gap-4">
            <div>
                <h3>开发建议</h3>
                <ul>
                    <li>使用CSS变量系统管理所有设计标记</li>
                    <li>建立组件库，确保UI元素的复用性</li>
                    <li>采用移动优先的响应式设计策略</li>
                    <li>使用语义化的HTML结构</li>
                    <li>遵循BEM或类似的CSS命名规范</li>
                </ul>
            </div>
            <div>
                <h3>质量保证</h3>
                <ul>
                    <li>定期进行设计系统一致性审查</li>
                    <li>使用自动化工具检测色彩对比度</li>
                    <li>进行跨浏览器兼容性测试</li>
                    <li>收集用户反馈并持续优化</li>
                    <li>建立设计系统文档和使用指南</li>
                </ul>
            </div>
        </div>
    </div>

    <footer class="section text-center">
        <h2>📋 总结</h2>
        <p>这套基于 #0FD59D 青绿色的设计系统提供了完整的视觉规范和UI组件库。通过统一的色彩、字体、间距和组件标准，确保产品界面的一致性和专业性。</p>

        <div class="flex justify-center mt-4">
            <button class="button button-primary">开始使用</button>
            <button class="button button-outline ml-4">下载资源</button>
        </div>

        <p class="small mt-4" style="color: var(--gray-500);">
            设计系统版本 v1.0 | 最后更新：2024年8月15日 |
            <a href="#" style="color: var(--primary-600);">查看更新日志</a>
        </p>
    </footer>
</body>
</html>