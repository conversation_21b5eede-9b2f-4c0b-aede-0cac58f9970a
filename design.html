<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网页视觉色彩方案与UI规范</title>
    <style>
        body {
            font-family: 'Se<PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1, h2, h3 {
            color: #0A5C36;
        }
        
        .color-palette {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 30px 0;
        }
        
        .color-box {
            width: 120px;
            height: 120px;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            padding: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            color: white;
            font-size: 14px;
        }
        
        .primary { background-color: #0FD59D; }
        .primary-dark { background-color: #0A5C36; }
        .primary-light { background-color: #7AE8C5; }
        .secondary { background-color: #4A6BFF; }
        .accent { background-color: #FF6B6B; }
        .neutral-dark { background-color: #2D3748; }
        .neutral { background-color: #718096; }
        .neutral-light { background-color: #E2E8F0; }
        .neutral-extra-light { background-color: #F7FAFC; }
        
        .ui-examples {
            margin: 40px 0;
            border-top: 1px solid #E2E8F0;
            padding-top: 30px;
        }
        
        .button {
            display: inline-block;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 600;
            text-decoration: none;
            margin: 10px;
            transition: all 0.2s ease;
        }
        
        .button-primary {
            background-color: #0FD59D;
            color: white;
        }
        
        .button-primary:hover {
            background-color: #0DBD8B;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(15, 213, 157, 0.3);
        }
        
        .button-secondary {
            background-color: #4A6BFF;
            color: white;
        }
        
        .button-secondary:hover {
            background-color: #3A5BEF;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(74, 107, 255, 0.3);
        }
        
        .button-accent {
            background-color: #FF6B6B;
            color: white;
        }
        
        .button-accent:hover {
            background-color: #EF5B5B;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }
        
        .button-outline {
            background-color: transparent;
            border: 2px solid #0FD59D;
            color: #0FD59D;
        }
        
        .button-outline:hover {
            background-color: rgba(15, 213, 157, 0.1);
        }
        
        .card {
            width: 300px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin: 20px;
        }
        
        .card-header {
            height: 120px;
            background-color: #0FD59D;
        }
        
        .card-body {
            padding: 20px;
            background-color: white;
        }
        
        .typography-example h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin: 20px 0;
        }
        
        .typography-example h2 {
            font-size: 2rem;
            font-weight: 700;
            margin: 18px 0;
        }
        
        .typography-example h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 16px 0;
        }
        
        .typography-example p {
            margin: 12px 0;
            font-size: 1rem;
            line-height: 1.6;
        }
        
        .form-control {
            margin: 15px 0;
        }
        
        .form-control label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #2D3748;
        }
        
        .form-control input, .form-control select, .form-control textarea {
            width: 100%;
            max-width: 400px;
            padding: 10px 12px;
            border: 1px solid #E2E8F0;
            border-radius: 6px;
            font-size: 16px;
            transition: border 0.2s ease;
        }
        
        .form-control input:focus, .form-control select:focus, .form-control textarea:focus {
            outline: none;
            border-color: #0FD59D;
            box-shadow: 0 0 0 3px rgba(15, 213, 157, 0.2);
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin: 15px 0;
        }
        
        .alert-success {
            background-color: #F0FFF4;
            color: #0A5C36;
            border-left: 4px solid #0FD59D;
        }
        
        .alert-info {
            background-color: #EBF8FF;
            color: #2C5282;
            border-left: 4px solid #4A6BFF;
        }
        
        .alert-warning {
            background-color: #FFFFF0;
            color: #975B16;
            border-left: 4px solid #ECC94B;
        }
        
        .alert-error {
            background-color: #FFF5F5;
            color: #9B2C2C;
            border-left: 4px solid #FF6B6B;
        }
        
        .spacing-scale {
            margin: 30px 0;
        }
        
        .spacing-item {
            margin: 10px 0;
            display: flex;
            align-items: center;
        }
        
        .spacing-box {
            display: inline-block;
            background-color: #0FD59D;
            margin-right: 15px;
        }
        
        .spacing-4 { width: 4px; height: 4px; }
        .spacing-8 { width: 8px; height: 8px; }
        .spacing-12 { width: 12px; height: 12px; }
        .spacing-16 { width: 16px; height: 16px; }
        .spacing-20 { width: 20px; height: 20px; }
        .spacing-24 { width: 24px; height: 24px; }
        .spacing-32 { width: 32px; height: 32px; }
        .spacing-40 { width: 40px; height: 40px; }
        .spacing-48 { width: 48px; height: 48px; }
        .spacing-64 { width: 64px; height: 64px; }
    </style>
</head>
<body>
    <h1>网页视觉色彩方案与UI规范</h1>
    <p>基于主色调 #0FD59D 的完整设计系统</p>
    
    <h2>1. 色彩方案</h2>
    <div class="color-palette">
        <div class="color-box primary">
            <strong>主色</strong>
            #0FD59D
        </div>
        <div class="color-box primary-dark">
            <strong>深主色</strong>
            #0A5C36
        </div>
        <div class="color-box primary-light">
            <strong>浅主色</strong>
            #7AE8C5
        </div>
        <div class="color-box secondary">
            <strong>辅助色</strong>
            #4A6BFF
        </div>
        <div class="color-box accent">
            <strong>强调色</strong>
            #FF6B6B
        </div>
        <div class="color-box neutral-dark">
            <strong>深中性色</strong>
            #2D3748
        </div>
        <div class="color-box neutral">
            <strong>中性色</strong>
            #718096
        </div>
        <div class="color-box neutral-light">
            <strong>浅中性色</strong>
            #E2E8F0
        </div>
        <div class="color-box neutral-extra-light">
            <strong>超浅中性色</strong>
            #F7FAFC
        </div>
    </div>
    
    <h3>色彩使用指南</h3>
    <ul>
        <li><strong>主色 (#0FD59D):</strong> 用于主要按钮、重要链接、品牌元素和关键操作</li>
        <li><strong>深主色 (#0A5C36):</strong> 用于标题、重要文本和悬停状态</li>
        <li><strong>浅主色 (#7AE8C5):</strong> 用于背景高亮、次要元素和禁用状态</li>
        <li><strong>辅助色 (#4A6BFF):</strong> 用于次要按钮、信息提示和次要操作</li>
        <li><strong>强调色 (#FF6B6B):</strong> 用于错误提示、删除操作和需要特别注意的元素</li>
        <li><strong>中性色系:</strong> 用于文本、边框、背景和不需要突出显示的内容</li>
    </ul>
    
    <div class="ui-examples">
        <h2>2. 按钮样式</h2>
        <a href="#" class="button button-primary">主要按钮</a>
        <a href="#" class="button button-secondary">次要按钮</a>
        <a href="#" class="button button-accent">强调按钮</a>
        <a href="#" class="button button-outline">轮廓按钮</a>
        
        <h3>按钮状态</h3>
        <a href="#" class="button button-primary" style="opacity: 0.7; cursor: not-allowed;">禁用状态</a>
        <a href="#" class="button button-primary" style="background-color: #0DBD8B;">悬停状态</a>
        <a href="#" class="button button-primary" style="background-color: #0A9C72;">激活状态</a>
    </div>
    
    <div class="ui-examples">
        <h2>3. 卡片设计</h2>
        <div class="card">
            <div class="card-header"></div>
            <div class="card-body">
                <h3>卡片标题</h3>
                <p>这是一个示例卡片，展示卡片的设计样式和排版。卡片可以包含各种内容，如图片、文本和按钮。</p>
                <a href="#" class="button button-primary" style="margin: 0; margin-top: 15px;">操作按钮</a>
            </div>
        </div>
    </div>
    
    <div class="ui-examples">
        <h2>4. 排版规范</h2>
        <div class="typography-example">
            <h1>标题1 (2.5rem/40px)</h1>
            <h2>标题2 (2rem/32px)</h2>
            <h3>标题3 (1.5rem/24px)</h3>
            <p>正文文本 (1rem/16px) - 这是常规正文文本的示例。行高设置为1.6以提高可读性。字体颜色使用#2D3748确保足够的对比度。</p>
            <p style="color: #718096;">次要文本 - 用于不太重要的信息，使用#718096颜色。</p>
            <p><a href="#" style="color: #0FD59D; text-decoration: none;">链接文本</a> - 链接使用主色，悬停时添加下划线。</p>
        </div>
    </div>
    
    <div class="ui-examples">
        <h2>5. 表单元素</h2>
        <div class="form-control">
            <label for="name">文本输入</label>
            <input type="text" id="name" placeholder="请输入您的姓名">
        </div>
        
        <div class="form-control">
            <label for="email">邮箱输入</label>
            <input type="email" id="email" placeholder="<EMAIL>">
        </div>
        
        <div class="form-control">
            <label for="password">密码输入</label>
            <input type="password" id="password" placeholder="请输入密码">
        </div>
        
        <div class="form-control">
            <label for="select">下拉选择</label>
            <select id="select">
                <option>选项1</option>
                <option>选项2</option>
                <option>选项3</option>
            </select>
        </div>
        
        <div class="form-control">
            <label for="textarea">多行文本</label>
            <textarea id="textarea" rows="4" placeholder="请输入您的反馈"></textarea>
        </div>
    </div>
    
    <div class="ui-examples">
        <h2>6. 提示信息</h2>
        <div class="alert alert-success">
            <strong>成功提示</strong> - 操作已成功完成。
        </div>
        
        <div class="alert alert-info">
            <strong>信息提示</strong> - 这是信息提示内容。
        </div>
        
        <div class="alert alert-warning">
            <strong>警告提示</strong> - 请注意潜在的问题。
        </div>
        
        <div class="alert alert-error">
            <strong>错误提示</strong> - 操作失败，请重试。
        </div>
    </div>
    
    <div class="ui-examples">
        <h2>7. 间距规范</h2>
        <div class="spacing-scale">
            <div class="spacing-item">
                <span class="spacing-box spacing-4"></span>
                <span>4px - 微小间距</span>
            </div>
            <div class="spacing-item">
                <span class="spacing-box spacing-8"></span>
                <span>8px - 元素内间距</span>
            </div>
            <div class="spacing-item">
                <span class="spacing-box spacing-12"></span>
                <span>12px - 小间距</span>
            </div>
            <div class="spacing-item">
                <span class="spacing-box spacing-16"></span>
                <span>16px - 基础间距</span>
            </div>
            <div class="spacing-item">
                <span class="spacing-box spacing-20"></span>
                <span>20px - 中等间距</span>
            </div>
            <div class="spacing-item">
                <span class="spacing-box spacing-24"></span>
                <span>24px - 标准间距</span>
            </div>
            <div class="spacing-item">
                <span class="spacing-box spacing-32"></span>
                <span>32px - 大间距</span>
            </div>
            <div class="spacing-item">
                <span class="spacing-box spacing-40"></span>
                <span>40px - 超大间距</span>
            </div>
            <div class="spacing-item">
                <span class="spacing-box spacing-48"></span>
                <span>48px - 区块间距</span>
            </div>
            <div class="spacing-item">
                <span class="spacing-box spacing-64"></span>
                <span>64px - 超大区块间距</span>
            </div>
        </div>
    </div>
    
    <div class="ui-examples">
        <h2>8. 圆角规范</h2>
        <ul>
            <li><strong>小圆角 (4px):</strong> 用于按钮、输入框等小元素</li>
            <li><strong>中等圆角 (6-8px):</strong> 用于卡片、弹出框等中等大小元素</li>
            <li><strong>大圆角 (12px):</strong> 用于大尺寸元素或需要特别强调的组件</li>
            <li><strong>全圆角 (50%):</strong> 用于圆形头像、图标等</li>
        </ul>
    </div>
    
    <div class="ui-examples">
        <h2>9. 阴影规范</h2>
        <ul>
            <li><strong>小阴影 (0 1px 3px rgba(0,0,0,0.1)):</strong> 用于悬浮按钮、小卡片</li>
            <li><strong>中等阴影 (0 4px 6px rgba(0,0,0,0.1)):</strong> 用于卡片、下拉菜单</li>
            <li><strong>大阴影 (0 10px 15px rgba(0,0,0,0.1)):</strong> 用于模态框、弹出层</li>
            <li><strong>特殊阴影 (0 4px 12px rgba(15, 213, 157, 0.3)):</strong> 用于主色元素的悬停状态</li>
        </ul>
    </div>
    
    <h2>10. 设计原则</h2>
    <ol>
        <li><strong>一致性:</strong> 所有UI元素保持一致的风格和交互方式</li>
        <li><strong>层次感:</strong> 通过色彩、大小和间距建立清晰的视觉层次</li>
        <li><strong>可访问性:</strong> 确保足够的色彩对比度(至少4.5:1)和可读性</li>
        <li><strong>响应式:</strong> 设计需适应不同屏幕尺寸和设备</li>
        <li><strong>情感化:</strong> 利用主色的活力感传达品牌个性</li>
    </ol>
    
    <h2>11. 实施建议</h2>
    <ul>
        <li>使用CSS变量或Sass/Less变量管理颜色和间距</li>
        <li>创建可复用的UI组件库</li>
        <li>在项目中建立设计标记(Design Tokens)系统</li>
        <li>对开发团队进行设计系统培训</li>
        <li>定期审查UI一致性</li>
    </ul>
</body>
</html>