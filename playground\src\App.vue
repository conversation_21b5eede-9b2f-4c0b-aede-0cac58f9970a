<template>
  <div class="playground">
    <header class="playground-header">
      <h1>LanZT UI Playground</h1>
      <p>Vue 3 组件库开发环境</p>
    </header>

    <main class="playground-main">
      <section class="demo-section">
        <h2>按钮组件</h2>
        <div class="demo-row">
          <lz-button type="primary">主要按钮</lz-button>
          <lz-button type="secondary">次要按钮</lz-button>
          <lz-button type="outline">轮廓按钮</lz-button>
          <lz-button type="ghost">幽灵按钮</lz-button>
          <lz-button type="danger">危险按钮</lz-button>
        </div>

        <div class="demo-row">
          <lz-button type="primary" size="small">小按钮</lz-button>
          <lz-button type="primary" size="medium">中按钮</lz-button>
          <lz-button type="primary" size="large">大按钮</lz-button>
        </div>

        <div class="demo-row">
          <lz-button type="primary" :loading="true">加载中</lz-button>
          <lz-button type="primary" :disabled="true">禁用状态</lz-button>
          <lz-button type="primary" icon="heart">带图标</lz-button>
          <lz-button type="primary" round>圆角按钮</lz-button>
          <lz-button type="primary" circle icon="plus" />
        </div>
      </section>

      <section class="demo-section">
        <h2>图标组件</h2>
        <div class="demo-row">
          <lz-icon name="heart" :size="24" color="#0FD59D" />
          <lz-icon name="star" :size="32" color="#F59E0B" />
          <lz-icon name="loading" :size="24" color="#4F46E5" spin />
          <lz-icon name="check-circle" :size="28" color="#10B981" />
          <lz-icon name="alert-circle" :size="28" color="#EF4444" />
        </div>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
// 组件逻辑
</script>

<style scoped>
.playground {
  min-height: 100vh;
  background-color: var(--lz-gray-50);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.playground-header {
  text-align: center;
  padding: var(--lz-space-8) var(--lz-space-4);
  background: white;
  border-bottom: 1px solid var(--lz-gray-200);
}

.playground-header h1 {
  font-size: var(--lz-text-4xl);
  font-weight: var(--lz-font-bold);
  color: var(--lz-gray-900);
  margin: 0 0 var(--lz-space-2) 0;
}

.playground-header p {
  font-size: var(--lz-text-lg);
  color: var(--lz-gray-600);
  margin: 0;
}

.playground-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--lz-space-8) var(--lz-space-4);
}

.demo-section {
  background: white;
  border-radius: var(--lz-radius-xl);
  padding: var(--lz-space-8);
  margin-bottom: var(--lz-space-8);
  box-shadow: var(--lz-shadow);
}

.demo-section h2 {
  font-size: var(--lz-text-2xl);
  font-weight: var(--lz-font-semibold);
  color: var(--lz-gray-900);
  margin: 0 0 var(--lz-space-6) 0;
}

.demo-row {
  display: flex;
  align-items: center;
  gap: var(--lz-space-4);
  margin-bottom: var(--lz-space-4);
  flex-wrap: wrap;
}

.demo-row:last-child {
  margin-bottom: 0;
}
</style>
